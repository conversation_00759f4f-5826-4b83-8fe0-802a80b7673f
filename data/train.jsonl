{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEffect of aspirin on cardiovascular mortality in elderly patients", "completion": "aspirin & cardiovascular & mortality & (elderly | geriatric)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMRI vs CT for detecting early stroke signs", "completion": "(MRI | \"magnetic resonance imaging\") & (CT | \"computed tomography\") & detect & (stroke | CVA) & early"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP levels in sepsis-induced ARDS", "completion": "(CRP | \"C-reactive protein\") & sepsis & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVentilator-associated pneumonia prevention with chlorhexidine", "completion": "(VAP | \"ventilator-associated pneumonia\") & prevention & (chlorhexidine | CHG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBeta-blockers in heart failure with preserved ejection fraction", "completion": "(beta-blockers | \"beta adrenergic blockers\") & (HFpEF | \"heart failure with preserved ejection fraction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDiagnostic accuracy of D-dimer for pulmonary embolism in pregnancy", "completion": "(D-dimer | \"fibrin degradation product\") & accuracy & (PE | \"pulmonary embolism\") & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContinuous glucose monitoring in type 1 diabetes management", "completion": "(CGM | \"continuous glucose monitoring\") & (T1D | \"type 1 diabetes\") & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNeonatal outcomes of antenatal corticosteroid administration", "completion": "neonatal & outcomes & (antenatal | prenatal) & corticosteroids"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRituximab for refractory myasthenia gravis", "completion": "rituximab & (refractory | resistant) & (MG | \"myasthenia gravis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltrasound guidance for central venous catheter insertion", "completion": "ultrasound & guidance & (CVC | \"central venous catheter\") & insertion"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAssociation between vitamin D deficiency and multiple sclerosis progression", "completion": "(vitamin D | calciferol) & deficiency & (MS | \"multiple sclerosis\") & progression"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPost-op atrial fibrillation after cardiac surgery", "completion": "(post-op | postoperative) & (AF | \"atrial fibrillation\") & (\"cardiac surgery\" | CABG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSensitivity of rapid strep test in pediatric pharyngitis", "completion": "sensitivity & (\"rapid strep test\" | RADT) & (pediatric | children) & pharyngitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTroponin elevation in renal failure without ACS", "completion": "troponin & elevation & (\"renal failure\" | CKD) & !(ACS | \"acute coronary syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBisphosphonate-related osteonecrosis of the jaw risk factors", "completion": "(bisphosphonates | BPs) & (ONJ | \"osteonecrosis of the jaw\") & (\"risk factors\" | etiology)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin-guided antibiotic therapy in ICU patients", "completion": "procalcitonin & guided & antibiotics & (ICU | \"intensive care unit\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTranscatheter aortic valve replacement vs surgical AVR in high-risk patients", "completion": "(TAVR | TAVI) & (SAVR | \"surgical aortic valve replacement\") & (\"high risk\" | elderly)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine for treatment-resistant depression", "completion": "ketamine & (\"treatment resistant depression\" | TRD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nColonoscopy surveillance intervals after polyp removal", "completion": "colonoscopy & surveillance & intervals & (polypectomy | \"polyp removal\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMachine learning algorithms for detecting diabetic retinopathy", "completion": "\"machine learning\" & algorithms & detect & (DR | \"diabetic retinopathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nImpact of tight glucose control on surgical wound healing", "completion": "\"tight glucose control\" & (\"surgical wounds\" | \"wound healing\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFalse positive rates in PSA screening for prostate cancer", "completion": "\"false positive\" & (PSA | \"prostate specific antigen\") & screening & (\"prostate cancer\" | PCa)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHeparin-induced thrombocytopenia diagnosis with 4T score", "completion": "(HIT | \"heparin-induced thrombocytopenia\") & diagnosis & (\"4T score\" | \"four T score\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVaccination hesitancy and measles outbreaks", "completion": "(vaccination | immunization) & hesitancy & (measles | rubeola) & outbreaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal microbiota transplantation for recurrent C. diff infection", "completion": "(FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (\"C. diff\" | \"Clostridium difficile\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal INR range in mechanical heart valve patients", "completion": "(INR | \"international normalized ratio\") & range & (\"mechanical heart valves\" | MHV)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymph node metastasis in early stage oral cancer", "completion": "(metastasis | metastatic) & (lymph nodes | LNs) & (\"oral cancer\" | \"oral squamous cell carcinoma\") & \"early stage\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAnticoagulation in cerebral venous thrombosis", "completion": "(anticoagulation | AC) & (CVT | \"cerebral venous thrombosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nScreening protocols for Lynch syndrome", "completion": "screening & protocols & (\"Lynch syndrome\" | HNPCC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNon-invasive ventilation in COPD exacerbations", "completion": "(NIV | \"non-invasive ventilation\") & (COPD | \"chronic obstructive pulmonary disease\") & exacerbations"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTranexamic acid for trauma patients with hemorrhagic shock", "completion": "(TXA | tranexamic) & trauma & (\"hemorrhagic shock\" | bleeding)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBiomarkers for early detection of ovarian cancer", "completion": "biomarkers & \"early detection\" & (\"ovarian cancer\" | EOC) & (CA-125 | HE4)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadiofrequency ablation vs surgery for small liver tumors", "completion": "(RFA | \"radiofrequency ablation\") & surgery & (\"liver tumors\" | HCC) & small"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPPI use and risk of C. diff infection", "completion": "(PPIs | \"proton pump inhibitors\") & risk & (\"C. diff\" | \"Clostridium difficile\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nImmunotherapy response in NSCLC with PD-L1 expression", "completion": "immunotherapy & response & (NSCLC | \"non-small cell lung cancer\") & (\"PD-L1\" | \"programmed death ligand 1\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEEG findings in non-convulsive status epilepticus", "completion": "(EEG | electroencephalography) & findings & (NCSE | \"non-convulsive status epilepticus\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal timing for initiation of renal replacement therapy", "completion": "timing & (RRT | \"renal replacement therapy\") & initiation & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGenetic markers for warfarin dosing", "completion": "(genetic | pharmacogenetic) & markers & (warfarin | coumadin) & dosing"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic ultrasound for pancreatic cyst diagnosis", "completion": "(EUS | \"endoscopic ultrasound\") & (\"pancreatic cysts\" | PCN) & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion thresholds in chemotherapy patients", "completion": "\"platelet transfusion\" & thresholds & (chemotherapy | chemo)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLong-term outcomes of bariatric surgery in adolescents", "completion": "\"long term\" & outcomes & (\"bariatric surgery\" | gastric) & adolescents"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFerritin as an acute phase reactant in sepsis", "completion": "ferritin & (\"acute phase reactant\" | APR) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPET-CT staging accuracy in Hodgkin's lymphoma", "completion": "(PET-CT | \"positron emission tomography\") & staging & accuracy & (\"Hodgkin''s lymphoma\" | HL)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotic prophylaxis in total joint arthroplasty", "completion": "(antibiotics | prophylaxis) & (TJA | \"total joint arthroplasty\" | \"joint replacement\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyroid storm diagnosis using Burch-Wartofsky criteria", "completion": "(thyroid storm | thyrotoxic) & diagnosis & (\"Burch-Wartofsky\" | BWS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRisk of progressive multifocal leukoencephalopathy with natalizumab", "completion": "risk & (PML | \"progressive multifocal leukoencephalopathy\") & (natalizumab | Tysabri)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nScreening for abdominal aortic aneurysm with ultrasound", "completion": "screening & (AAA | \"abdominal aortic aneurysm\") & ultrasound"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressin vs norepinephrine in septic shock", "completion": "(vasopressin | ADH) & (norepinephrine | noradrenaline) & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCervical cancer screening guidelines with HPV co-testing", "completion": "(screening | guidelines) & (\"cervical cancer\" | cervix) & (HPV | \"human papillomavirus\") & co-testing"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDeep brain stimulation for Parkinson's disease tremor", "completion": "(DBS | \"deep brain stimulation\") & (\"Parkinson''s disease\" | PD) & tremor"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMortality benefit of therapeutic hypothermia after cardiac arrest", "completion": "mortality & benefit & (hypothermia | cooling) & (\"cardiac arrest\" | SCA)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptical coherence tomography in glaucoma progression", "completion": "(OCT | \"optical coherence tomography\") & (glaucoma | POAG) & progression"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal amphotericin B for mucormycosis treatment", "completion": "(\"liposomal amphotericin\" | AmBisome) & (mucormycosis | zygomycosis) & treatment"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPredictors of difficult airway in obese patients", "completion": "predictors & (\"difficult airway\" | intubation) & (obese | obesity)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal monitoring patterns in placental abruption", "completion": "(fetal | cardiotocography) & monitoring & (\"placental abruption\" | abruption)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nStatins and new-onset diabetes risk", "completion": "(statins | HMG-CoA) & (\"new onset diabetes\" | NOD) & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTEE-guided cardioversion for atrial fibrillation", "completion": "(TEE | \"transesophageal echocardiography\") & guided & (cardioversion | DCCV) & (AF | \"atrial fibrillation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRRT vs intermittent hemodialysis in acute kidney injury", "completion": "(CRRT | \"continuous renal replacement\") & (IHD | \"intermittent hemodialysis\") & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBRCA mutation carriers and contralateral breast cancer risk", "completion": "(BRCA | \"breast cancer gene\") & carriers & (contralateral | bilateral) & (\"breast cancer\" | BC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDiagnostic yield of capsule endoscopy for obscure GI bleeding", "completion": "(yield | accuracy) & (\"capsule endoscopy\" | VCE) & (\"obscure GI bleeding\" | OGIB)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum procalcitonin to guide antibiotic duration in pneumonia", "completion": "procalcitonin & guide & antibiotics & duration & (pneumonia | CAP)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril-valsartan in heart failure with reduced ejection fraction", "completion": "(sacubitril/valsartan | Entresto) & (HFrEF | \"heart failure with reduced ejection fraction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal PEEP setting in ARDS management", "completion": "(PEEP | \"positive end-expiratory pressure\") & setting & (ARDS | \"acute respiratory distress syndrome\") & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgical site infection rates with mechanical bowel prep", "completion": "(SSI | \"surgical site infection\") & rates & (\"mechanical bowel preparation\" | MBP)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTroponin I vs Troponin T in chronic kidney disease", "completion": "(troponin I | TnI) & (troponin T | TnT) & (CKD | \"chronic kidney disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFirst-line immunotherapy for metastatic melanoma", "completion": "(first-line | 1L) & immunotherapy & (metastatic | stage IV) & melanoma"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntraoperative neuromonitoring in thyroid surgery", "completion": "(IONM | \"intraoperative neuromonitoring\") & (thyroid | parathyroid) & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin K antagonist reversal with PCC", "completion": "(VKA | \"vitamin K antagonist\") & reversal & (PCC | \"prothrombin complex concentrate\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nYield of next-generation sequencing in undiagnosed genetic disorders", "completion": "(yield | diagnostic) & (NGS | \"next generation sequencing\") & (\"genetic disorders\" | syndromes) & undiagnosed"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltrasound elastography for liver fibrosis staging", "completion": "(ultrasound | sonoelastography) & elastography & (\"liver fibrosis\" | cirrhosis) & staging"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nApixaban vs warfarin in antiphospholipid syndrome", "completion": "(apixaban | Eliquis) & (warfarin | coumadin) & (APS | \"antiphospholipid syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRisk stratification for SUDEP in epilepsy patients", "completion": "(risk | stratification) & (SUDEP | \"sudden unexpected death in epilepsy\") & (epilepsy | seizures)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum free light chains in multiple myeloma diagnosis", "completion": "(SFLC | \"serum free light chains\") & (\"multiple myeloma\" | MM) & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTransradial vs transfemoral access in cardiac catheterization", "completion": "(transradial | radial) & (transfemoral | femoral) & (\"cardiac catheterization\" | angiography)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of dual antiplatelet therapy after DES", "completion": "duration & (DAPT | \"dual antiplatelet therapy\") & (DES | \"drug eluting stent\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI for CNS anomalies detection", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (CNS | \"central nervous system\") & anomalies"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProphylactic antibiotics in neutropenic fever", "completion": "(prophylactic | prophylaxis) & antibiotics & (neutropenic | neutropenia) & fever"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDexamethasone for prevention of post-op nausea and vomiting", "completion": "(dexamethasone | Decadron) & prevention & (PONV | \"postoperative nausea and vomiting\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMicrobiome changes in inflammatory bowel disease", "completion": "(microbiome | microbiota) & changes & (IBD | \"inflammatory bowel disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSensitivity of FDG-PET for occult malignancy detection", "completion": "sensitivity & (FDG-PET | \"fluorodeoxyglucose pet\") & (occult | unknown) & malignancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCryoablation for Barrett's esophagus treatment", "completion": "(cryoablation | freezing) & (\"Barrett''s esophagus\" | BE) & treatment"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nB-type natriuretic peptide levels in heart failure diagnosis", "completion": "(BNP | \"B-type natriuretic peptide\") & levels & (HF | \"heart failure\") & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntra-articular hyaluronic acid for knee osteoarthritis", "completion": "(intra-articular | IA) & (hyaluronic | viscosupplementation) & (knee | OA | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRisk of TLS with venetoclax in CLL", "completion": "risk & (TLS | \"tumor lysis syndrome\") & (venetoclax | Venclexta) & (CLL | \"chronic lymphocytic leukemia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPercutaneous drainage vs surgery for diverticular abscess", "completion": "(percutaneous | PCD) & drainage & surgery & (diverticular | diverticulitis) & abscess"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for left ventricular assist devices", "completion": "(INR | \"international normalized ratio\") & range & (LVAD | \"left ventricular assist device\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nImmunohistochemistry markers for mesothelioma diagnosis", "completion": "(IHC | immunohistochemistry) & markers & (mesothelioma | pleural) & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLidocaine patch for post-herpetic neuralgia", "completion": "(lidocaine | lignocaine) & patch & (PHN | \"post-herpetic neuralgia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPhenytoin vs levetiracetam for seizure prophylaxis in TBI", "completion": "(phenytoin | Dilantin) & (levetiracetam | Keppra) & (seizures | epilepsy) & prophylaxis & (TBI | \"traumatic brain injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nScreening colonoscopy intervals for ulcerative colitis patients", "completion": "screening & colonoscopy & intervals & (UC | \"ulcerative colitis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFractional flow reserve vs angiography for PCI guidance", "completion": "(FFR | \"fractional flow reserve\") & (angiography | coronary) & (PCI | \"percutaneous coronary intervention\") & guidance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAmyloid PET for Alzheimer's disease diagnosis", "completion": "(amyloid | beta-amyloid) & (PET | \"positron emission tomography\") & (AD | \"Alzheimer''s disease\") & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin vs vancomycin for initial C. diff treatment", "completion": "(fidaxomicin | Dificid) & (vancomycin | Vancocin) & (\"C. diff\" | \"Clostridium difficile\") & treatment"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCervical length measurement for preterm birth prediction", "completion": "(cervical | cervix) & length & measurement & (preterm | premature) & birth & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDirect oral anticoagulants in cancer-associated thrombosis", "completion": "(DOACs | \"direct oral anticoagulants\") & (cancer | malignancy) & (thrombosis | VTE)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of endoscopy in upper GI bleeding", "completion": "timing & endoscopy & (\"upper GI bleed\" | UGIB)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSGLT2 inhibitors for heart failure without diabetes", "completion": "(SGLT2 | \"sodium-glucose cotransporter-2\") & inhibitors & (HF | \"heart failure\") & !diabetes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntraoperative cell salvage in obstetrics", "completion": "(ICS | \"intraoperative cell salvage\") & (obstetrics | OB) & (hemorrhage | bleeding)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTransthoracic echocardiography for infective endocarditis diagnosis", "completion": "(TTE | \"transthoracic echocardiography\") & (IE | \"infective endocarditis\") & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCryoprecipitate transfusion in hypofibrinogenemia", "completion": "(cryoprecipitate | cryo) & transfusion & (hypofibrinogenemia | fibrinogen)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMolecular testing for EGFR mutations in lung adenocarcinoma", "completion": "(molecular | genetic) & testing & (EGFR | \"epidermal growth factor receptor\") & (\"lung cancer\" | NSCLC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPreoperative carbohydrate loading in elective surgery", "completion": "(preoperative | pre-op) & (carbohydrate | CHO) & loading & (elective | scheduled) & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacral neuromodulation for fecal incontinence", "completion": "(neuromodulation | SNS) & (fecal | bowel) & incontinence"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff pressure in endotracheal intubation", "completion": "cuff & pressure & (endotracheal | ET) & intubation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlasma exchange for thrombotic thrombocytopenic purpura", "completion": "(TPE | \"therapeutic plasma exchange\") & (TTP | \"thrombotic thrombocytopenic purpura\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCoronary calcium score for cardiovascular risk assessment", "completion": "(CAC | \"coronary artery calcium\") & score & (CVD | \"cardiovascular disease\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgical approaches for recurrent inguinal hernia repair", "completion": "surgical & approaches & (recurrent | recurrence) & (\"inguinal hernia\" | groin)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal HbA1c target in elderly diabetics", "completion": "(HbA1c | \"glycated hemoglobin\") & target & (elderly | geriatric) & (diabetes | DM)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBone mineral density changes with aromatase inhibitors", "completion": "(BMD | \"bone mineral density\") & changes & (AIs | \"aromatase inhibitors\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntracranial pressure monitoring in traumatic brain injury", "completion": "(ICP | \"intracranial pressure\") & monitoring & (TBI | \"traumatic brain injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTelemedicine for chronic disease management in rural areas", "completion": "(telemedicine | telehealth) & (chronic | long-term) & management & rural"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDiagnostic criteria for systemic lupus erythematosus", "completion": "(diagnostic | classification) & criteria & (SLE | \"systemic lupus erythematosus\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal echocardiography for congenital heart defect screening", "completion": "(fetal | prenatal) & echocardiography & (CHD | \"congenital heart defects\") & screening"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet function testing in PCI patients", "completion": "(platelet | aggregation) & testing & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVaccination strategies during measles outbreaks", "completion": "(vaccination | immunization) & strategies & (measles | rubeola) & outbreaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal sedation protocol for mechanical ventilation", "completion": "(sedation | analgesedation) & protocol & (\"mechanical ventilation\" | MV)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAccuracy of rapid influenza diagnostic tests", "completion": "(accuracy | sensitivity) & (RIDT | \"rapid influenza test\") & (influenza | flu)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTamsulosin for ureteral stone expulsion", "completion": "(tamsulosin | Flomax) & (ureteral | ureteric) & (stones | urolithiasis) & expulsion"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRisk of PML with rituximab therapy", "completion": "risk & (PML | \"progressive multifocal leukoencephalopathy\") & (rituximab | Rituxan)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContinuous EEG monitoring in subarachnoid hemorrhage", "completion": "(cEEG | \"continuous EEG\") & monitoring & (SAH | \"subarachnoid hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of anticoagulation for DVT", "completion": "duration & (anticoagulation | AC) & (DVT | \"deep vein thrombosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI for myocarditis diagnosis", "completion": "(CMR | \"cardiac MRI\") & (myocarditis | inflammatory) & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum lactate as predictor of sepsis mortality", "completion": "(lactate | lactic) & predictor & (sepsis | septic) & mortality"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid injections for carpal tunnel syndrome", "completion": "(corticosteroids | steroids) & injections & (CTS | \"carpal tunnel syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal blood pressure target in hemorrhagic stroke", "completion": "(BP | \"blood pressure\") & target & (hemorrhagic | ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphocyte count as prognostic marker in COVID-19", "completion": "(lymphocyte | lymphopenia) & (prognostic | marker) & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine ablation for differentiated thyroid cancer", "completion": "(RAI | radioiodine) & ablation & (DTC | \"differentiated thyroid cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntravesical BCG for non-muscle invasive bladder cancer", "completion": "(intravesical | bladder) & (BCG | \"Bacillus Calmette-Gu\u00e9rin\") & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal P2Y12 inhibitor in STEMI patients", "completion": "(P2Y12 | ADP) & inhibitor & (STEMI | \"ST elevation MI\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSensitivity of fine needle aspiration for thyroid nodules", "completion": "sensitivity & (FNA | \"fine needle aspiration\") & (thyroid | nodules)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine for post-op pain control", "completion": "(liposomal | Exparel) & bupivacaine & (post-op | postoperative) & pain"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRisk of contrast-induced nephropathy with IV contrast", "completion": "risk & (CIN | \"contrast-induced nephropathy\") & (\"IV contrast\" | \"iodinated contrast\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal INR range in pregnancy with mechanical valves", "completion": "(INR | \"international ratio\") & range & pregnancy & (\"mechanical valves\" | MHV)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic mucosal resection for early gastric cancer", "completion": "(EMR | \"endoscopic mucosal resection\") & (early | T1) & (\"gastric cancer\" | stomach)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTroponin elevation in pulmonary embolism", "completion": "troponin & elevation & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP as predictor of appendicitis severity", "completion": "(CRP | \"C-reactive protein\") & predictor & (appendicitis | appendix) & severity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal surveillance for Barrett's esophagus", "completion": "surveillance & (\"Barrett''s esophagus\" | BE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPembrolizumab for metastatic non-small cell lung cancer", "completion": "(pembrolizumab | Keytruda) & (metastatic | stage IV) & (NSCLC | \"non-small cell lung cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVentricular tachycardia ablation in structural heart disease", "completion": "(VT | \"ventricular tachycardia\") & ablation & (\"structural heart disease\" | cardiomyopathy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin to guide antibiotic cessation in sepsis", "completion": "procalcitonin & guide & antibiotics & cessation & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of surgery in infective endocarditis", "completion": "timing & surgery & (IE | \"infective endocarditis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer for ruling out DVT in pregnancy", "completion": "(D-dimer | \"fibrin fragment\") & (\"rule out\" | exclusion) & (DVT | \"deep vein thrombosis\") & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum amyloid A in rheumatoid arthritis activity", "completion": "(SAA | \"serum amyloid A\") & (RA | \"rheumatoid arthritis\") & activity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for cirrhosis with portal vein thrombosis", "completion": "(INR | \"international ratio\") & target & (cirrhosis | liver) & (PVT | \"portal vein thrombosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTranscranial Doppler for vasospasm detection in SAH", "completion": "(TCD | \"transcranial Doppler\") & (vasospasm | VSP) & detection & (SAH | \"subarachnoid hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nChest tube size for pneumothorax drainage", "completion": "(chest tube | thoracostomy) & size & (pneumothorax | PTX) & drainage"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAdalimumab for ulcerative colitis maintenance", "completion": "(adalimumab | Humira) & (UC | \"ulcerative colitis\") & (maintenance | remission)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal hemoglobin threshold for RBC transfusion", "completion": "(hemoglobin | Hb) & threshold & (\"RBC transfusion\" | \"blood transfusion\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgical resection vs SBRT for early lung cancer", "completion": "(surgery | resection) & (SBRT | \"stereotactic body radiotherapy\") & (early | stage I) & (\"lung cancer\" | NSCLC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFlecainide for atrial fibrillation rhythm control", "completion": "(flecainide | Tambocor) & (AF | \"atrial fibrillation\") & (\"rhythm control\" | cardioversion)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTranexamic acid in total knee arthroplasty", "completion": "(TXA | tranexamic) & (TKA | \"total knee arthroplasty\") & (bleeding | blood)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of antibiotics for osteomyelitis", "completion": "duration & antibiotics & (osteomyelitis | bone)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCardiac troponin in aortic stenosis patients", "completion": "(troponin | cTn) & (AS | \"aortic stenosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy for Crohn's disease assessment", "completion": "(capsule | VCE) & endoscopy & (\"Crohn''s disease\" | CD) & assessment"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBispectral index monitoring during general anesthesia", "completion": "(BIS | \"bispectral index\") & monitoring & (anesthesia | sedation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum cortisol for adrenal insufficiency diagnosis", "completion": "(cortisol | hydrocortisone) & serum & (\"adrenal insufficiency\" | Addison''s) & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff pressure in laryngeal mask airway", "completion": "cuff & pressure & (LMA | \"laryngeal mask airway\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin for recurrent Clostridium difficile infection", "completion": "(fidaxomicin | Dificid) & (recurrent | relapse) & (\"C. diff\" | \"Clostridium difficile\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of endoscopy in variceal bleeding", "completion": "timing & endoscopy & (variceal | esophageal) & bleeding"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSensitivity of CT angiography for pulmonary embolism", "completion": "sensitivity & (CTA | \"CT angiography\") & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressor choice in neurogenic shock", "completion": "(vasopressor | pressor) & choice & (neurogenic | spinal) & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrinary NGAL for acute kidney injury prediction", "completion": "(uNGAL | \"urinary neutrophil gelatinase-associated lipocalin\") & (AKI | \"acute kidney injury\") & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal PEEP setting in obese patients during surgery", "completion": "(PEEP | \"positive end-expiratory pressure\") & setting & (obese | obesity) & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril-valsartan for heart failure with preserved EF", "completion": "(sacubitril/valsartan | Entresto) & (HFpEF | \"heart failure with preserved ejection fraction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion threshold in preeclampsia", "completion": "(platelet | PLT) & transfusion & threshold & (preeclampsia | PET)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for atrial fibrillation with mechanical valve", "completion": "(INR | \"international ratio\") & target & (AF | \"atrial fibrillation\") & (\"mechanical valve\" | MVR)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI for congenital diaphragmatic hernia", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgical outcomes of robotic vs laparoscopic hysterectomy", "completion": "outcomes & (robotic | da Vinci) & (laparoscopic | keyhole) & hysterectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP as predictor of response to biologics in RA", "completion": "(CRP | \"C-reactive protein\") & predictor & response & (biologics | TNF) & (RA | \"rheumatoid arthritis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of DAPT after coronary stenting", "completion": "duration & (DAPT | \"dual antiplatelet therapy\") & (stenting | PCI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum procalcitonin for bacterial vs viral differentiation", "completion": "procalcitonin & (bacterial | infection) & (viral | virus) & differentiation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVaginal progesterone for preterm birth prevention", "completion": "(vaginal | intravaginal) & progesterone & (preterm | premature) & birth & prevention"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal sedation for alcohol withdrawal syndrome", "completion": "sedation & (AWS | \"alcohol withdrawal\") & (delirium | DT)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTransthoracic echo for pericardial effusion detection", "completion": "(TTE | \"transthoracic echocardiography\") & (\"pericardial effusion\" | tamponade) & detection"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntra-articular steroids for knee osteoarthritis pain", "completion": "(intra-articular | IA) & steroids & (knee | OA | osteoarthritis) & pain"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of surgery in acute cholecystitis", "completion": "timing & surgery & (\"acute cholecystitis\" | gallbladder)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum ferritin as marker of macrophage activation", "completion": "ferritin & marker & (\"macrophage activation\" | HLH)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff size for blood pressure measurement", "completion": "cuff & size & (BP | \"blood pressure\") & measurement"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal biometry accuracy for gestational age dating", "completion": "(fetal | ultrasound) & biometry & accuracy & (\"gestational age\" | GA) & dating"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors for familial hypercholesterolemia", "completion": "(PCSK9 | proprotein) & inhibitors & (FH | \"familial hypercholesterolemia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal dose of vitamin D for deficiency treatment", "completion": "dose & (vitamin D | cholecalciferol) & deficiency & treatment"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum sodium correction rate in hyponatremia", "completion": "(sodium | Na) & correction & rate & (hyponatremia | low)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTranscatheter closure for patent foramen ovale", "completion": "(transcatheter | percutaneous) & closure & (PFO | \"patent foramen ovale\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of antibiotics for cellulitis", "completion": "duration & antibiotics & (cellulitis | skin)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI for arrhythmogenic right ventricular dysplasia", "completion": "(CMR | \"cardiac MRI\") & (ARVC | \"arrhythmogenic right ventricular cardiomyopathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin to guide antibiotics in COPD exacerbations", "completion": "procalcitonin & guide & antibiotics & (COPD | \"chronic obstructive pulmonary disease\") & exacerbations"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for left atrial appendage closure", "completion": "(INR | \"international ratio\") & target & (LAAC | \"left atrial appendage closure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTroponin I vs T for myocardial infarction diagnosis", "completion": "(troponin I | TnI) & (troponin T | TnT) & (MI | \"myocardial infarction\") & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSensitivity of PET-CT for lymphoma staging", "completion": "sensitivity & (PET-CT | \"positron emission tomography\") & (lymphoma | HL | NHL) & staging"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressin in vasodilatory shock", "completion": "(vasopressin | ADH) & (vasodilatory | septic) & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrine microscopy for acute pyelonephritis diagnosis", "completion": "urine & microscopy & (pyelonephritis | UTI) & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal PEEP for one-lung ventilation", "completion": "(PEEP | \"positive end-expiratory pressure\") & setting & (\"one lung\" | OLV)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacral nerve stimulation for urinary incontinence", "completion": "(SNS | \"sacral nerve stimulation\") & (urinary | bladder) & incontinence"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet count as predictor of preeclampsia severity", "completion": "(platelet | thrombocyte) & count & predictor & (preeclampsia | PET) & severity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of surgery in acute appendicitis", "completion": "timing & surgery & (\"acute appendicitis\" | appendix)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum free light chains in amyloidosis diagnosis", "completion": "(SFLC | \"serum free light chains\") & (amyloidosis | AL) & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTransesophageal echo for left atrial thrombus", "completion": "(TEE | \"transesophageal echocardiography\") & (\"left atrial\" | LA) & thrombus"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff pressure in tracheostomy tubes", "completion": "cuff & pressure & (tracheostomy | trach)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin for first C. diff infection", "completion": "(fidaxomicin | Dificid) & (first | initial) & (\"C. diff\" | \"Clostridium difficile\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSensitivity of MRI for prostate cancer detection", "completion": "sensitivity & (MRI | \"magnetic resonance\") & (\"prostate cancer\" | PCa) & detection"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressor choice in anaphylactic shock", "completion": "(vasopressor | epinephrine) & choice & (anaphylactic | anaphylaxis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrinary albumin-creatinine ratio for CKD staging", "completion": "(ACR | \"albumin creatinine ratio\") & (CKD | \"chronic kidney disease\") & staging"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal PEEP in ARDS with COVID-19", "completion": "(PEEP | \"positive end-expiratory pressure\") & setting & (ARDS | \"acute respiratory distress syndrome\") & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacituzumab govitecan for triple-negative breast cancer", "completion": "(sacituzumab | Trodelvy) & (TNBC | \"triple negative breast cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet function testing in CABG patients", "completion": "(platelet | aggregation) & testing & (CABG | \"coronary artery bypass\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for cirrhotic patients with PVT", "completion": "(INR | \"international ratio\") & target & (cirrhosis | liver) & (PVT | \"portal vein thrombosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal echocardiography for tetralogy of Fallot", "completion": "(fetal | prenatal) & echocardiography & (\"tetralogy of Fallot\" | TOF)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgical resection vs radiotherapy for acoustic neuroma", "completion": "(surgery | resection) & (radiotherapy | SRS) & (\"acoustic neuroma\" | schwannoma)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP as predictor of failure in appendicitis antibiotics", "completion": "(CRP | \"C-reactive protein\") & predictor & failure & antibiotics & appendicitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of anticoagulation for PE", "completion": "duration & anticoagulation & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum cortisol for Cushing's syndrome diagnosis", "completion": "cortisol & serum & (\"Cushing''s syndrome\" | hypercortisolism) & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTranscranial Doppler for brain death confirmation", "completion": "(TCD | \"transcranial Doppler\") & (\"brain death\" | cerebral)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nChest tube management for pleural effusion", "completion": "(chest tube | thoracostomy) & management & (\"pleural effusion\" | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAdalimumab for Crohn's fistula healing", "completion": "(adalimumab | Humira) & (\"Crohn''s disease\" | CD) & (fistula | perianal)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal hemoglobin for transfusion in GI bleed", "completion": "(hemoglobin | Hb) & threshold & transfusion & (\"GI bleed\" | hemorrhage)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSBRT vs surgery for renal cell carcinoma", "completion": "(SBRT | \"stereotactic body radiotherapy\") & surgery & (RCC | \"renal cell carcinoma\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFlecainide for supraventricular tachycardia", "completion": "(flecainide | Tambocor) & (SVT | \"supraventricular tachycardia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTranexamic acid in spine surgery", "completion": "(TXA | tranexamic) & (spine | spinal) & surgery & (bleeding | blood)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of antibiotics for UTI", "completion": "duration & antibiotics & (UTI | \"urinary tract infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCardiac troponin in cardiac contusion", "completion": "troponin & (\"cardiac contusion\" | trauma)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy for obscure GI bleeding", "completion": "(capsule | VCE) & endoscopy & (\"obscure GI bleeding\" | OGIB)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring in ICU sedation", "completion": "(BIS | \"bispectral index\") & monitoring & (ICU | \"intensive care\") & sedation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum cortisol in septic shock", "completion": "cortisol & serum & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff pressure in pediatric intubation", "completion": "cuff & pressure & (pediatric | children) & intubation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin for severe C. diff infection", "completion": "(fidaxomicin | Dificid) & severe & (\"C. diff\" | \"Clostridium difficile\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of endoscopy in lower GI bleed", "completion": "timing & endoscopy & (\"lower GI bleed\" | LGIB)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSensitivity of CT for renal colic", "completion": "sensitivity & (CT | \"computed tomography\") & (\"renal colic\" | stones)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressor choice in cardiogenic shock", "completion": "(vasopressor | pressor) & choice & (cardiogenic | cardiac) & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrinary NGAL for contrast nephropathy", "completion": "(uNGAL | \"urinary neutrophil gelatinase-associated lipocalin\") & (CIN | \"contrast induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal PEEP in morbid obesity", "completion": "(PEEP | \"positive end-expiratory pressure\") & setting & (\"morbid obesity\" | bariatric)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril-valsartan for acute decompensated HF", "completion": "(sacubitril/valsartan | Entresto) & (ADHF | \"acute decompensated heart failure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion in ITP", "completion": "(platelet | PLT) & transfusion & (ITP | \"immune thrombocytopenia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for VTE prophylaxis", "completion": "(INR | \"international ratio\") & target & (VTE | \"venous thromboembolism\") & prophylaxis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI for spina bifida", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"spina bifida\" | myelomeningocele)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgical outcomes of open vs endovascular AAA repair", "completion": "outcomes & (open | surgical) & (endovascular | EVAR) & (AAA | \"abdominal aortic aneurysm\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP as predictor of IBD flare", "completion": "(CRP | \"C-reactive protein\") & predictor & (IBD | \"inflammatory bowel disease\") & flare"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of DAPT in NSTEMI", "completion": "duration & (DAPT | \"dual antiplatelet therapy\") & (NSTEMI | \"non ST elevation MI\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum procalcitonin for meningitis etiology", "completion": "procalcitonin & (meningitis | CNS) & (bacterial | viral) & etiology"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVaginal estrogen for GSM symptoms", "completion": "(vaginal | topical) & estrogen & (GSM | \"genitourinary syndrome of menopause\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal sedation for ICU delirium", "completion": "sedation & (ICU | \"intensive care\") & (delirium | agitation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTransthoracic echo for endocarditis", "completion": "(TTE | \"transthoracic echocardiography\") & (endocarditis | IE)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntra-articular hyaluronan for hip OA", "completion": "(intra-articular | IA) & (hyaluronan | hyaluronic) & (hip | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of cholecystectomy in gallstone pancreatitis", "completion": "timing & cholecystectomy & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum ferritin in hemophagocytic syndrome", "completion": "ferritin & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff size for obese patients", "completion": "cuff & size & (obese | obesity) & (BP | \"blood pressure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal biometry for growth restriction", "completion": "(fetal | ultrasound) & biometry & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors for statin intolerance", "completion": "(PCSK9 | proprotein) & inhibitors & (statin | HMG-CoA) & intolerance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal vitamin D dose for osteoporosis", "completion": "dose & (vitamin D | cholecalciferol) & osteoporosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerum sodium in SIADH", "completion": "(sodium | Na) & (SIADH | \"syndrome of inappropriate ADH\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTranscatheter ASD closure", "completion": "(transcatheter | percutaneous) & closure & (ASD | \"atrial septal defect\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal antibiotic duration for bacteremia", "completion": "duration & antibiotics & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI for sarcoidosis", "completion": "(CMR | \"cardiac MRI\") & (sarcoidosis | granulomatous)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance in sepsis resuscitation", "completion": "(lactate | lactic) & clearance & sepsis & (resuscitation | EGDT)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid injections for plantar fasciitis", "completion": "(steroids | corticosteroids) & injections & (\"plantar fasciitis\" | heel)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBP target in ischemic stroke", "completion": "(BP | \"blood pressure\") & target & (ischemic | thrombotic) & stroke"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphocyte count in COVID-19 prognosis", "completion": "(lymphocyte | lymphopenia) & (COVID | SARS-CoV-2) & prognosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine for toxic nodular goiter", "completion": "(RAI | radioiodine) & (\"toxic nodular goiter\" | MNG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG for bladder cancer recurrence", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & (\"bladder cancer\" | TCC) & recurrence"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal P2Y12 inhibitor in NSTEMI", "completion": "(P2Y12 | ADP) & inhibitor & (NSTEMI | \"non ST elevation MI\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSensitivity of FNA for parotid tumors", "completion": "sensitivity & (FNA | \"fine needle aspiration\") & (parotid | salivary)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in hernia repair", "completion": "(liposomal | Exparel) & bupivacaine & (hernia | inguinal) & repair"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRisk of CIN with contrast CT", "completion": "risk & (CIN | \"contrast induced nephropathy\") & (\"contrast CT\" | \"iodinated contrast\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal INR in pregnancy", "completion": "(INR | \"international ratio\") & pregnancy & anticoagulation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEMR for early esophageal cancer", "completion": "(EMR | \"endoscopic mucosal resection\") & early & (esophageal | EAC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTroponin in myocarditis", "completion": "troponin & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP in diverticulitis", "completion": "(CRP | \"C-reactive protein\") & diverticulitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurveillance for Barrett's esophagus", "completion": "surveillance & (\"Barrett''s esophagus\" | BE)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPembrolizumab for NSCLC", "completion": "(pembrolizumab | Keytruda) & (NSCLC | \"non-small cell lung cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVT ablation in cardiomyopathy", "completion": "(VT | \"ventricular tachycardia\") & ablation & cardiomyopathy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin for antibiotic stopping", "completion": "procalcitonin & antibiotics & cessation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgery timing in IE", "completion": "surgery & timing & (IE | \"infective endocarditis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer for DVT exclusion", "completion": "(D-dimer | \"fibrin fragment\") & (DVT | \"deep vein thrombosis\") & (\"rule out\" | exclusion)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSAA in rheumatoid arthritis", "completion": "(SAA | \"serum amyloid A\") & (RA | \"rheumatoid arthritis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nINR target in PVT", "completion": "(INR | \"international ratio\") & target & (PVT | \"portal vein thrombosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTCD for vasospasm", "completion": "(TCD | \"transcranial Doppler\") & vasospasm"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nChest tube for hemothorax", "completion": "(chest tube | thoracostomy) & hemothorax"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAdalimumab for UC", "completion": "(adalimumab | Humira) & (UC | \"ulcerative colitis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHb threshold for transfusion", "completion": "(hemoglobin | Hb) & threshold & transfusion"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSBRT for lung cancer", "completion": "(SBRT | \"stereotactic body radiotherapy\") & (\"lung cancer\" | NSCLC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFlecainide for AF", "completion": "(flecainide | Tambocor) & (AF | \"atrial fibrillation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTXA in hip replacement", "completion": "(TXA | tranexamic) & (THA | \"total hip arthroplasty\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotic duration for cellulitis", "completion": "duration & antibiotics & cellulitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTroponin in aortic dissection", "completion": "troponin & (\"aortic dissection\" | dissection)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy for CD", "completion": "(capsule | VCE) & endoscopy & (CD | \"Crohn''s disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring in TIVA", "completion": "(BIS | \"bispectral index\") & monitoring & (TIVA | \"total intravenous anesthesia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCortisol for adrenal crisis", "completion": "cortisol & (\"adrenal crisis\" | insufficiency)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff pressure in LMA", "completion": "cuff & pressure & (LMA | \"laryngeal mask airway\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin for C. diff", "completion": "(fidaxomicin | Dificid) & (\"C. diff\" | \"Clostridium difficile\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopy timing in UGIB", "completion": "endoscopy & timing & (UGIB | \"upper GI bleed\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSensitivity of CTA for PE", "completion": "sensitivity & (CTA | \"CT angiography\") & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressor in neurogenic shock", "completion": "vasopressor & (neurogenic | spinal) & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nuNGAL for AKI", "completion": "(uNGAL | \"urinary neutrophil gelatinase-associated lipocalin\") & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPEEP in obesity", "completion": "(PEEP | \"positive end-expiratory pressure\") & obesity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEntresto for HF", "completion": "(Entresto | sacubitril/valsartan) & (HF | \"heart failure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion in PET", "completion": "(platelet | PLT) & transfusion & (PET | preeclampsia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nINR target in AF", "completion": "(INR | \"international ratio\") & target & (AF | \"atrial fibrillation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI for CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic vs laparoscopic colectomy", "completion": "(robotic | da Vinci) & laparoscopic & colectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP for biologic response", "completion": "(CRP | \"C-reactive protein\") & (biologics | TNF) & response"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDAPT duration after stent", "completion": "duration & (DAPT | \"dual antiplatelet therapy\") & stent"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin for viral vs bacterial", "completion": "procalcitonin & (viral | bacterial) & differentiation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProgesterone for preterm birth", "completion": "progesterone & (preterm | premature) & birth"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSedation for AWS", "completion": "sedation & (AWS | \"alcohol withdrawal\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTTE for pericardial effusion", "completion": "(TTE | \"transthoracic echocardiography\") & (\"pericardial effusion\" | tamponade)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIA steroids for OA", "completion": "(intra-articular | IA) & steroids & (OA | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgery timing for cholecystitis", "completion": "surgery & timing & cholecystitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFerritin in HLH", "completion": "ferritin & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff size for BP", "completion": "cuff & size & (BP | \"blood pressure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBiometry for GA", "completion": "(fetal | ultrasound) & biometry & (GA | \"gestational age\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 for FH", "completion": "(PCSK9 | proprotein) & inhibitors & (FH | \"familial hypercholesterolemia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D for deficiency", "completion": "(vitamin D | cholecalciferol) & deficiency"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSodium in hyponatremia", "completion": "sodium & hyponatremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nASD closure", "completion": "(ASD | \"atrial septal defect\") & closure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for bacteremia", "completion": "antibiotics & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCMR for myocarditis", "completion": "(CMR | \"cardiac MRI\") & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate in sepsis", "completion": "lactate & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid injections for CTS", "completion": "steroids & injections & (CTS | \"carpal tunnel syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBP target in ICH", "completion": "(BP | \"blood pressure\") & target & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphocyte in COVID", "completion": "lymphocyte & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRAI for thyroid cancer", "completion": "(RAI | radioiodine) & (\"thyroid cancer\" | carcinoma)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG for NMIBC", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nP2Y12 for STEMI", "completion": "(P2Y12 | ADP) & inhibitor & (STEMI | \"ST elevation MI\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFNA for thyroid", "completion": "(FNA | \"fine needle aspiration\") & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine for pain", "completion": "(liposomal | Exparel) & bupivacaine & pain"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCIN risk with contrast", "completion": "risk & (CIN | \"contrast induced nephropathy\") & contrast"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nINR in pregnancy", "completion": "(INR | \"international ratio\") & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEMR for EGC", "completion": "(EMR | \"endoscopic mucosal resection\") & (EGC | \"early gastric cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTroponin in PE", "completion": "troponin & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP in appendicitis", "completion": "(CRP | \"C-reactive protein\") & appendicitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBarrett's surveillance", "completion": "surveillance & (\"Barrett''s esophagus\" | BE)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPembrolizumab for lung cancer", "completion": "(pembrolizumab | Keytruda) & (\"lung cancer\" | NSCLC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVT ablation", "completion": "(VT | \"ventricular tachycardia\") & ablation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin stop antibiotics", "completion": "procalcitonin & antibiotics & cessation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgery for IE", "completion": "surgery & (IE | \"infective endocarditis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer rule out DVT", "completion": "(D-dimer | \"fibrin fragment\") & (\"rule out\" | exclusion) & (DVT | \"deep vein thrombosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSAA in RA", "completion": "(SAA | \"serum amyloid A\") & (RA | \"rheumatoid arthritis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nINR for PVT", "completion": "(INR | \"international ratio\") & (PVT | \"portal vein thrombosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTCD for SAH", "completion": "(TCD | \"transcranial Doppler\") & (SAH | \"subarachnoid hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nChest tube for pneumothorax", "completion": "(chest tube | thoracostomy) & pneumothorax"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAdalimumab for colitis", "completion": "(adalimumab | Humira) & (colitis | UC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHb for transfusion", "completion": "(hemoglobin | Hb) & transfusion"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSBRT for lung", "completion": "(SBRT | \"stereotactic body radiotherapy\") & lung"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFlecainide for SVT", "completion": "(flecainide | Tambocor) & (SVT | \"supraventricular tachycardia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTXA for TKA", "completion": "(TXA | tranexamic) & (TKA | \"total knee arthroplasty\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for UTI", "completion": "antibiotics & (UTI | \"urinary tract infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTroponin in contusion", "completion": "troponin & contusion"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule for CD", "completion": "(capsule | VCE) & endoscopy & (CD | \"Crohn''s disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS for sedation", "completion": "(BIS | \"bispectral index\") & sedation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCortisol for crisis", "completion": "cortisol & crisis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff for LMA", "completion": "cuff & (LMA | \"laryngeal mask airway\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin for CDI", "completion": "(fidaxomicin | Dificid) & (CDI | \"C. diff infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopy for UGIB", "completion": "endoscopy & (UGIB | \"upper GI bleed\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCTA for PE", "completion": "(CTA | \"CT angiography\") & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressor for shock", "completion": "vasopressor & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nuNGAL for AKI", "completion": "(uNGAL | \"urinary neutrophil gelatinase-associated lipocalin\") & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPEEP for ARDS", "completion": "(PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEntresto for HF", "completion": "(Entresto | sacubitril/valsartan) & (HF | \"heart failure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet for PET", "completion": "platelet & (PET | preeclampsia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nINR for AF", "completion": "(INR | \"international ratio\") & (AF | \"atrial fibrillation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMRI for CDH", "completion": "(MRI | \"magnetic resonance\") & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic for colectomy", "completion": "robotic & colectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP for biologics", "completion": "(CRP | \"C-reactive protein\") & biologics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDAPT after PCI", "completion": "(DAPT | \"dual antiplatelet therapy\") & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin for stopping", "completion": "procalcitonin & antibiotics & cessation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProgesterone for PTB", "completion": "progesterone & (PTB | \"preterm birth\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSedation for withdrawal", "completion": "sedation & withdrawal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTTE for effusion", "completion": "(TTE | \"transthoracic echocardiography\") & effusion"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroids for OA", "completion": "steroids & (OA | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgery for cholecystitis", "completion": "surgery & cholecystitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFerritin for HLH", "completion": "ferritin & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff for BP", "completion": "cuff & (BP | \"blood pressure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBiometry for IUGR", "completion": "biometry & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 for hypercholesterolemia", "completion": "(PCSK9 | proprotein) & inhibitors & hypercholesterolemia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D for bones", "completion": "(vitamin D | cholecalciferol) & (bones | osteoporosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSodium correction", "completion": "sodium & correction & hyponatremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nASD repair", "completion": "(ASD | \"atrial septal defect\") & repair"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for blood infection", "completion": "antibiotics & (bacteremia | \"blood infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMRI for heart inflammation", "completion": "(MRI | \"magnetic resonance\") & (myocarditis | inflammation) & heart"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate for sepsis", "completion": "lactate & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroids for carpal tunnel", "completion": "steroids & (\"carpal tunnel\" | CTS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBP in brain bleed", "completion": "(BP | \"blood pressure\") & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymph in COVID", "completion": "lymphocyte & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRAI for cancer", "completion": "(RAI | radioiodine) & (\"thyroid cancer\" | carcinoma)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG for bladder", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & bladder"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nP2Y12 for heart attack", "completion": "(P2Y12 | ADP) & inhibitor & (MI | \"heart attack\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFNA for nodules", "completion": "(FNA | \"fine needle aspiration\") & nodules"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBupivacaine for surgery pain", "completion": "(bupivacaine | local) & surgery & pain"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContrast kidney risk", "completion": "risk & (CIN | \"contrast nephropathy\") & contrast"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood thinner in pregnancy", "completion": "(anticoagulation | \"blood thinner\") & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nScope for stomach cancer", "completion": "endoscopy & (\"stomach cancer\" | gastric)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTroponin in lung clot", "completion": "troponin & (PE | \"lung clot\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP for appendix", "completion": "(CRP | \"C-reactive protein\") & appendicitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nScope for Barrett's", "completion": "endoscopy & (\"Barrett''s esophagus\" | BE)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKeytruda for lung", "completion": "(pembrolizumab | Keytruda) & lung"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAblation for VT", "completion": "ablation & (VT | \"ventricular tachycardia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCT stop abx", "completion": "procalcitonin & antibiotics & cessation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSurgery for endocarditis", "completion": "surgery & endocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer for clot", "completion": "(D-dimer | \"fibrin fragment\") & (DVT | clot)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSAA for arthritis", "completion": "(SAA | \"serum amyloid A\") & arthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nINR for liver clot", "completion": "(INR | \"international ratio\") & (PVT | \"liver clot\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTCD for brain bleed", "completion": "(TCD | \"transcranial Doppler\") & (SAH | \"brain bleed\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTube for collapsed lung", "completion": "(chest tube | thoracostomy) & (pneumothorax | \"collapsed lung\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHumira for UC", "completion": "(adalimumab | Humira) & (UC | \"ulcerative colitis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood level for transfusion", "completion": "(hemoglobin | Hb) & transfusion"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadiation for lung tumor", "completion": "(SBRT | radiotherapy) & (\"lung tumor\" | cancer)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTambocor for fast heart", "completion": "(flecainide | Tambocor) & (tachycardia | \"fast heart\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTXA for knee replacement", "completion": "(TXA | tranexamic) & (\"knee replacement\" | TKA)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for bladder infection", "completion": "antibiotics & (UTI | \"bladder infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHeart enzyme in trauma", "completion": "troponin & trauma"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPill cam for Crohn's", "completion": "(capsule | \"pill cam\") & endoscopy & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBrain monitor for anesthesia", "completion": "(BIS | monitor) & anesthesia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid for adrenal problem", "completion": "cortisol & (\"adrenal problem\" | insufficiency)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff for airway", "completion": "cuff & (airway | LMA)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDificid for diarrhea", "completion": "(fidaxomicin | Dificid) & (diarrhea | CDI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nScope for vomit blood", "completion": "endoscopy & (hematemesis | \"vomit blood\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCT scan for lung clot", "completion": "(CT | \"computed tomography\") & (PE | \"lung clot\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPressor for low BP", "completion": "(vasopressor | pressor) & (\"low BP\" | hypotension)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrine test for kidney injury", "completion": "(urine | uNGAL) & (AKI | \"kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBreathing machine setting", "completion": "(PEEP | ventilator) & setting"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEntresto for weak heart", "completion": "(Entresto | sacubitril/valsartan) & (\"weak heart\" | HF)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelets for preeclampsia", "completion": "platelets & transfusion & preeclampsia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood thinner level", "completion": "(INR | \"international ratio\") & (warfarin | \"blood thinner\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBaby MRI for hernia", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (CDH | hernia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobot surgery for colon", "completion": "robotic & surgery & (colon | colectomy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nInflammation marker for biologics", "completion": "(CRP | ESR) & biologics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTwo blood thinners after stent", "completion": "(DAPT | \"dual antiplatelet\") & stent"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCT to stop antibiotics", "completion": "procalcitonin & antibiotics & cessation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProgesterone to prevent early birth", "completion": "progesterone & prevention & (\"early birth\" | preterm)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSedation for DT", "completion": "sedation & (DT | \"delirium tremens\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHeart echo for fluid", "completion": "(echo | echocardiography) & (effusion | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid shot for arthritis", "completion": "(steroid | corticosteroid) & injection & arthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOperation for gallbladder", "completion": "surgery & (gallbladder | cholecystectomy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFerritin for HLH", "completion": "ferritin & (HLH | \"macrophage activation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBP cuff size", "completion": "cuff & size & (BP | \"blood pressure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltrasound for baby growth", "completion": "(ultrasound | sonogram) & (\"baby growth\" | IUGR)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 for high cholesterol", "completion": "(PCSK9 | proprotein) & inhibitors & (\"high cholesterol\" | hyperlipidemia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D for bones", "completion": "(vitamin D | cholecalciferol) & (bones | osteoporosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSodium low correction", "completion": "sodium & correction & hyponatremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHole in heart repair", "completion": "(ASD | \"hole in heart\") & repair"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for blood infection", "completion": "antibiotics & (bacteremia | \"blood infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMRI for heart inflammation", "completion": "(MRI | \"magnetic resonance\") & (myocarditis | \"heart inflammation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate in infection", "completion": "lactate & (infection | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid injection for wrist pain", "completion": "steroid & injection & (\"wrist pain\" | CTS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBP control in brain bleed", "completion": "(BP | \"blood pressure\") & control & (ICH | \"brain bleed\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhite blood cell in COVID", "completion": "(lymphocyte | WBC) & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine for thyroid", "completion": "(RAI | radioiodine) & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG for bladder cancer", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & (\"bladder cancer\" | TCC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood thinner after stent", "completion": "(P2Y12 | \"blood thinner\") & stent"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNeedle test for thyroid lump", "completion": "(FNA | \"needle test\") & (\"thyroid lump\" | nodule)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLong-lasting numbing for surgery", "completion": "(liposomal | long) & bupivacaine & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDye kidney damage", "completion": "(contrast | dye) & (nephropathy | \"kidney damage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWarfarin in pregnancy", "completion": "(warfarin | coumadin) & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCamera pill for Crohn's", "completion": "(capsule | \"camera pill\") & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBrain wave monitor", "completion": "(BIS | EEG) & monitor"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCortisol for Addison's", "completion": "cortisol & (Addison''s | \"adrenal insufficiency\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAirway cuff pressure", "completion": "cuff & pressure & airway"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDiarrhea antibiotic", "completion": "antibiotics & (diarrhea | CDI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nScope for stomach bleed", "completion": "endoscopy & (\"stomach bleed\" | UGIB)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCT for lung clot", "completion": "(CT | \"computed tomography\") & (PE | \"lung clot\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPressure drug for shock", "completion": "(vasopressor | pressor) & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrine marker for kidney injury", "completion": "(urine | biomarker) & (AKI | \"kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVentilator setting", "completion": "(PEEP | ventilator) & setting"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHeart failure pill", "completion": "(Entresto | sacubitril) & (\"heart failure\" | HF)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion in pregnancy sickness", "completion": "platelet & transfusion & (preeclampsia | \"pregnancy sickness\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood test for warfarin", "completion": "(INR | \"international ratio\") & (warfarin | coumadin)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal scan for hernia", "completion": "(fetal | ultrasound) & scan & (CDH | hernia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobot colon surgery", "completion": "robotic & (colon | colectomy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP for biologic drug", "completion": "(CRP | \"C-reactive protein\") & biologics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTwo blood thinners after heart stent", "completion": "(DAPT | \"dual antiplatelet\") & (stent | PCI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nInfection marker stop antibiotics", "completion": "procalcitonin & antibiotics & cessation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHormone prevent preterm birth", "completion": "progesterone & prevention & (\"preterm birth\" | premature)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCalming DT", "completion": "sedation & (DT | \"delirium tremens\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHeart ultrasound fluid", "completion": "(echo | echocardiography) & (effusion | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKnee shot for arthritis", "completion": "injection & (knee | OA) & arthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRemove gallbladder", "completion": "surgery & (gallbladder | cholecystectomy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIron protein in HLH", "completion": "ferritin & (HLH | \"macrophage activation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff too small BP", "completion": "cuff & size & (BP | \"blood pressure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBaby ultrasound size", "completion": "(fetal | ultrasound) & (size | growth)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNew drug high cholesterol", "completion": "(PCSK9 | evolocumab) & (\"high cholesterol\" | LDL)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D bone strength", "completion": "(vitamin D | cholecalciferol) & (bones | osteoporosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorrect low sodium", "completion": "sodium & correction & hyponatremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFix heart hole", "completion": "repair & (ASD | \"heart hole\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotics blood infection", "completion": "antibiotics & (bacteremia | \"blood infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMRI heart muscle", "completion": "(MRI | \"magnetic resonance\") & (myocarditis | \"heart muscle\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactic acid sepsis", "completion": "(lactate | \"lactic acid\") & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid shot wrist", "completion": "steroid & injection & wrist"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBP management hemorrhagic stroke", "completion": "(BP | \"blood pressure\") & management & (hemorrhagic | ICH)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymph count coronavirus", "completion": "lymphocyte & (COVID | coronavirus)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIodine treatment thyroid", "completion": "(RAI | radioiodine) & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBladder cancer immunotherapy", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & (\"bladder cancer\" | TCC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAnti-platelet after angioplasty", "completion": "(P2Y12 | \"anti-platelet\") & (stent | angioplasty)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNeedle biopsy thyroid", "completion": "(FNA | biopsy) & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nExtended release local anesthetic", "completion": "(liposomal | extended) & bupivacaine"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContrast media nephrotoxicity", "completion": "(contrast | dye) & (nephropathy | nephrotoxicity)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood thinner pregnancy risk", "completion": "(warfarin | \"blood thinner\") & pregnancy & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSwallowable camera Crohn's", "completion": "(capsule | \"swallowable camera\") & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAnesthesia depth monitor", "completion": "(BIS | \"bispectral index\") & monitor"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAdrenal hormone replacement", "completion": "cortisol & (\"adrenal insufficiency\" | replacement)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTube pressure breathing", "completion": "cuff & pressure & (intubation | breathing)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nC. diff antibiotic", "completion": "antibiotics & (\"C. diff\" | CDI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscope upper GI hemorrhage", "completion": "endoscopy & (\"upper GI bleed\" | UGIB)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nComputed tomography pulmonary embolism", "completion": "(CT | \"computed tomography\") & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressor choice hypotension", "completion": "(vasopressor | pressor) & choice & hypotension"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBiomarker acute kidney failure", "completion": "(uNGAL | biomarker) & (AKI | \"acute kidney failure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVentilator PEEP setting", "completion": "(PEEP | \"positive end-expiratory pressure\") & setting"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril valsartan heart function", "completion": "(sacubitril/valsartan | Entresto) & (\"heart failure\" | HF)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThrombocytopenia pregnancy hypertension", "completion": "(platelet | thrombocytopenia) & (preeclampsia | \"pregnancy hypertension\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCoagulation test warfarin", "completion": "(INR | \"international ratio\") & (warfarin | coumadin)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPrenatal MRI diaphragmatic hernia", "completion": "(prenatal | fetal) & (MRI | \"magnetic resonance\") & (CDH | \"diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic-assisted colectomy", "completion": "robotic & colectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP biologic response", "completion": "(CRP | \"C-reactive protein\") & biologics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDAPT duration coronary stent", "completion": "duration & (DAPT | \"dual antiplatelet\") & (stent | coronary)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin antibiotic duration", "completion": "procalcitonin & antibiotics & duration"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProgesterone preterm prevention", "completion": "progesterone & prevention & preterm"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSedation alcohol withdrawal", "completion": "sedation & (\"alcohol withdrawal\" | AWS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEchocardiogram pericardial fluid", "completion": "(echo | echocardiography) & (\"pericardial fluid\" | effusion)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntra-articular injection osteoarthritis", "completion": "(intra-articular | IA) & injection & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCholecystectomy timing", "completion": "cholecystectomy & timing"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFerritin hemophagocytic syndrome", "completion": "ferritin & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood pressure cuff size", "completion": "cuff & size & (BP | \"blood pressure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltrasound fetal growth", "completion": "ultrasound & fetal & growth"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitor hyperlipidemia", "completion": "(PCSK9 | evolocumab) & inhibitor & hyperlipidemia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D osteoporosis", "completion": "(vitamin D | cholecalciferol) & osteoporosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHyponatremia correction rate", "completion": "hyponatremia & correction & rate"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAtrial septal defect closure", "completion": "(ASD | \"atrial septal defect\") & closure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotics bacteremia", "completion": "antibiotics & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI myocarditis", "completion": "(CMR | \"cardiac MRI\") & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate septic shock", "completion": "lactate & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid injection carpal tunnel", "completion": "steroid & injection & (\"carpal tunnel\" | CTS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood pressure ICH", "completion": "(BP | \"blood pressure\") & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphopenia COVID-19", "completion": "(lymphopenia | lymphocyte) & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine ablation thyroid", "completion": "(RAI | radioiodine) & ablation & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG immunotherapy bladder", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & immunotherapy & bladder"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nP2Y12 inhibitor PCI", "completion": "(P2Y12 | ADP) & inhibitor & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFNA thyroid nodule", "completion": "(FNA | \"fine needle aspiration\") & (\"thyroid nodule\" | nodule)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine analgesia", "completion": "(liposomal | Exparel) & bupivacaine & analgesia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContrast-induced AKI", "completion": "(contrast | dye) & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWarfarin embryopathy", "completion": "warfarin & (embryopathy | pregnancy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy IBD", "completion": "(capsule | VCE) & endoscopy & (IBD | \"inflammatory bowel disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS depth anesthesia", "completion": "(BIS | \"bispectral index\") & depth & anesthesia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone adrenal insufficiency", "completion": "(hydrocortisone | cortisol) & (\"adrenal insufficiency\" | Addison''s)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndotracheal tube cuff", "completion": "(endotracheal | ET) & tube & cuff"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotic-associated diarrhea", "completion": "antibiotics & (diarrhea | CDI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEGD variceal bleeding", "completion": "(EGD | endoscopy) & (variceal | bleeding)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCTPA pulmonary embolism", "completion": "(CTPA | \"CT pulmonary angiogram\") & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNorepinephrine septic shock", "completion": "(norepinephrine | noradrenaline) & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNGAL kidney injury", "completion": "(NGAL | \"neutrophil gelatinase-associated lipocalin\") & (AKI | \"kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMechanical ventilation PEEP", "completion": "(mechanical ventilation | MV) & (PEEP | \"positive end-expiratory pressure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nARNI heart failure", "completion": "(ARNI | sacubitril/valsartan) & (\"heart failure\" | HF)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHELLP syndrome platelets", "completion": "(HELLP | \"hemolysis elevated liver enzymes low platelets\") & (platelet | PLT)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nINR monitoring warfarin", "completion": "(INR | \"international ratio\") & monitoring & (warfarin | coumadin)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal imaging congenital hernia", "completion": "(fetal | prenatal) & imaging & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMinimally invasive colectomy", "completion": "(robotic | laparoscopic) & colectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nInflammatory marker TNF inhibitor", "completion": "(CRP | ESR) & (TNF | biologics)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDual antiplatelet therapy duration", "completion": "duration & (DAPT | \"dual antiplatelet therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCT-guided antibiotic therapy", "completion": "(PCT | procalcitonin) & guided & antibiotics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProgestin preterm prevention", "completion": "(progesterone | progestin) & prevention & preterm"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBenzodiazepine alcohol withdrawal", "completion": "(benzodiazepine | BZD) & (\"alcohol withdrawal\" | AWS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPericardial effusion imaging", "completion": "(echo | imaging) & (\"pericardial effusion\" | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nJoint injection osteoarthritis", "completion": "joint & injection & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAcute cholecystitis surgery", "completion": "(acute | surgery) & cholecystitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHyperferritinemia HLH", "completion": "(hyperferritinemia | ferritin) & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff hypertension measurement", "completion": "cuff & (hypertension | BP) & measurement"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSonogram fetal development", "completion": "(sonogram | ultrasound) & fetal & development"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 mAb hypercholesterolemia", "completion": "(PCSK9 | \"proprotein convertase subtilisin/kexin type 9\") & (mAb | inhibitor) & hypercholesterolemia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCholecalciferol bone health", "completion": "(cholecalciferol | \"vitamin D\") & (\"bone health\" | osteoporosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHyponatremia management", "completion": "hyponatremia & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nASD percutaneous closure", "completion": "(ASD | \"atrial septal defect\") & (percutaneous | closure)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotic stewardship bacteremia", "completion": "antibiotics & stewardship & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCardiac magnetic resonance inflammation", "completion": "(CMR | \"cardiac MRI\") & inflammation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactic acid septicemia", "completion": "(lactic | lactate) & septicemia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid injection neuropathy", "completion": "(corticosteroid | steroid) & injection & (neuropathy | CTS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHypertension control hemorrhage", "completion": "(hypertension | BP) & control & (hemorrhage | ICH)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphocyte depletion coronavirus", "completion": "(lymphocyte | lymphopenia) & (COVID | coronavirus)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nI-131 thyroid ablation", "completion": "(I-131 | radioiodine) & ablation & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntravesical BCG carcinoma", "completion": "(intravesical | bladder) & (BCG | \"Bacillus Calmette-Gu\u00e9rin\") & (carcinoma | cancer)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nClopidogrel stent thrombosis", "completion": "(clopidogrel | Plavix) & (stent | thrombosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCytology thyroid nodule", "completion": "(cytology | FNA) & (\"thyroid nodule\" | nodule)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSustained release bupivacaine", "completion": "(sustained | liposomal) & bupivacaine"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadiocontrast nephropathy", "completion": "(radiocontrast | dye) & nephropathy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCoumarin pregnancy", "completion": "(coumarin | warfarin) & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWireless endoscopy IBD", "completion": "(wireless | capsule) & endoscopy & (IBD | \"inflammatory bowel disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEEG-derived depth monitor", "completion": "(EEG | \"bispectral index\") & monitor"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGlucocorticoid adrenal crisis", "completion": "(glucocorticoid | hydrocortisone) & (\"adrenal crisis\" | insufficiency)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTracheal tube cuff pressure", "completion": "(tracheal | endotracheal) & tube & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nC. difficile antimicrobial", "completion": "antimicrobials & (\"C. difficile\" | CDI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTherapeutic endoscopy hemorrhage", "completion": "(therapeutic | endoscopy) & hemorrhage"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nComputed tomographic angiography PE", "completion": "(CTA | \"computed tomographic angiography\") & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCatecholamine septic shock", "completion": "(catecholamine | norepinephrine) & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRenal biomarker AKI", "completion": "(renal | uNGAL) & biomarker & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPositive end-expiratory pressure ARDS", "completion": "(PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin receptor-neprilysin inhibitor HF", "completion": "(ARNI | sacubitril/valsartan) & (HF | \"heart failure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThrombocytopenia preeclampsia", "completion": "(thrombocytopenia | platelet) & preeclampsia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nInternational normalized ratio anticoagulation", "completion": "(INR | \"international normalized ratio\") & anticoagulation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPrenatal diagnosis CDH", "completion": "(prenatal | fetal) & diagnosis & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobot-assisted colorectal surgery", "completion": "(robot-assisted | robotic) & (colorectal | colectomy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nC-reactive protein anti-TNF", "completion": "(CRP | \"C-reactive protein\") & (\"anti-TNF\" | biologics)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDAPT coronary intervention", "completion": "(DAPT | \"dual antiplatelet therapy\") & (\"coronary intervention\" | PCI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin antimicrobial stewardship", "completion": "procalcitonin & (antimicrobial | antibiotic) & stewardship"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProgesterone preterm birth prevention", "completion": "progesterone & (\"preterm birth\" | PTB) & prevention"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSedative-hypnotic alcohol withdrawal", "completion": "(sedative | benzodiazepine) & (\"alcohol withdrawal\" | AWS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEchocardiographic pericardial effusion", "completion": "(echocardiographic | echo) & (\"pericardial effusion\" | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntra-articular corticosteroid OA", "completion": "(intra-articular | IA) & corticosteroid & (OA | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLaparoscopic cholecystectomy timing", "completion": "laparoscopic & cholecystectomy & timing"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFerritin hemophagocytic syndrome", "completion": "ferritin & (\"hemophagocytic syndrome\" | HLH)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSphygmomanometer cuff hypertension", "completion": "(sphygmomanometer | cuff) & hypertension"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nObstetric ultrasound growth", "completion": "(obstetric | fetal) & ultrasound & growth"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibition LDL", "completion": "(PCSK9 | inhibitor) & (LDL | cholesterol)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D3 bone density", "completion": "(vitamin D3 | cholecalciferol) & (\"bone density\" | osteoporosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHyponatremia correction protocol", "completion": "hyponatremia & correction & protocol"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTranscatheter ASD closure", "completion": "transcatheter & (ASD | \"atrial septal defect\") & closure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntimicrobial therapy bacteremia", "completion": "(antimicrobial | antibiotic) & therapy & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCMR cardiac inflammation", "completion": "(CMR | \"cardiac MRI\") & inflammation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate sepsis mortality", "completion": "lactate & sepsis & mortality"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid injection median neuropathy", "completion": "steroid & injection & (\"median neuropathy\" | CTS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood pressure management ICH", "completion": "(BP | \"blood pressure\") & management & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphopenia SARS-CoV-2", "completion": "lymphopenia & (SARS-CoV-2 | COVID)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioactive iodine thyroid", "completion": "(radioactive | I-131) & iodine & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG instillation bladder cancer", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & instillation & (\"bladder cancer\" | TCC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nP2Y12 receptor antagonist stent", "completion": "(P2Y12 | antagonist) & stent"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFine-needle aspiration thyroid", "completion": "(FNA | \"fine-needle aspiration\") & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDepot bupivacaine formulation", "completion": "(depot | liposomal) & bupivacaine"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIodinated contrast nephropathy", "completion": "(iodinated | contrast) & nephropathy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin K antagonist pregnancy", "completion": "(VKA | \"vitamin K antagonist\") & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule enteroscopy Crohn's", "completion": "(capsule | enteroscopy) & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBispectral index sedation", "completion": "(BIS | \"bispectral index\") & sedation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid adrenal support", "completion": "corticosteroid & (\"adrenal support\" | insufficiency)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndotracheal cuff pressure", "completion": "(endotracheal | ET) & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotic C. difficile colitis", "completion": "antibiotics & (\"C. difficile\" | CDI) & colitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic hemostasis", "completion": "endoscopic & hemostasis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCT angiogram pulmonary embolism", "completion": "(CTA | \"CT angiogram\") & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressor sepsis", "completion": "vasopressor & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNGAL acute renal injury", "completion": "(NGAL | \"neutrophil gelatinase-associated lipocalin\") & (AKI | \"acute renal injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMechanical ventilation settings", "completion": "(mechanical ventilation | MV) & settings"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan heart failure", "completion": "(sacubitril/valsartan | Entresto) & (\"heart failure\" | HF)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHELLP thrombocytopenia", "completion": "(HELLP | \"hemolysis elevated liver enzymes low platelets\") & thrombocytopenia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nINR warfarin monitoring", "completion": "(INR | \"international normalized ratio\") & (warfarin | coumadin) & monitoring"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI diaphragmatic hernia", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"diaphragmatic hernia\" | CDH)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic colectomy outcomes", "completion": "robotic & colectomy & outcomes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP TNF inhibitor response", "completion": "(CRP | \"C-reactive protein\") & (\"TNF inhibitor\" | biologics) & response"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDAPT after PCI duration", "completion": "duration & (DAPT | \"dual antiplatelet therapy\") & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin antibiotic duration", "completion": "procalcitonin & antibiotic & duration"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProgesterone prevent preterm", "completion": "progesterone & prevent & preterm"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSedation AWS", "completion": "sedation & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEcho pericardial fluid", "completion": "(echo | echocardiography) & (\"pericardial fluid\" | effusion)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nJoint injection arthritis", "completion": "joint & injection & arthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCholecystectomy acute", "completion": "cholecystectomy & acute"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFerritin HLH diagnosis", "completion": "ferritin & (HLH | \"hemophagocytic lymphohistiocytosis\") & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBP measurement cuff", "completion": "(BP | \"blood pressure\") & measurement & cuff"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltrasound fetal growth assessment", "completion": "ultrasound & fetal & (\"growth assessment\" | IUGR)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitor cholesterol", "completion": "(PCSK9 | inhibitor) & cholesterol"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D bone mineral density", "completion": "(vitamin D | cholecalciferol) & (\"bone mineral density\" | BMD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHyponatremia treatment", "completion": "hyponatremia & treatment"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nASD repair percutaneous", "completion": "(ASD | \"atrial septal defect\") & repair & percutaneous"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotic selection bacteremia", "completion": "antibiotics & selection & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI inflammation", "completion": "(CMR | \"cardiac MRI\") & inflammation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate level sepsis", "completion": "lactate & level & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid shot carpal tunnel", "completion": "steroid & injection & (\"carpal tunnel\" | CTS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBP control hemorrhagic stroke", "completion": "(BP | \"blood pressure\") & control & (hemorrhagic | ICH)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphocyte count COVID", "completion": "lymphocyte & count & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nI-131 ablation thyroid", "completion": "(I-131 | radioiodine) & ablation & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG for bladder carcinoma", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & (\"bladder carcinoma\" | TCC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nClopidogrel after stent", "completion": "(clopidogrel | Plavix) & stent"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFNA thyroid diagnosis", "completion": "(FNA | \"fine needle aspiration\") & thyroid & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nExtended-release local anesthetic", "completion": "(extended-release | liposomal) & bupivacaine"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContrast-induced AKI", "completion": "(contrast | dye) & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWarfarin in gestation", "completion": "warfarin & (gestation | pregnancy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy Crohn's disease", "completion": "(capsule | VCE) & endoscopy & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS anesthesia depth", "completion": "(BIS | \"bispectral index\") & anesthesia & depth"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone for Addison's", "completion": "hydrocortisone & (Addison''s | \"adrenal insufficiency\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nET tube cuff pressure", "completion": "(ET | \"endotracheal tube\") & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntimicrobial C. diff", "completion": "antimicrobials & (\"C. diff\" | CDI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic treatment bleeding", "completion": "endoscopic & treatment & bleeding"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCT PE diagnosis", "completion": "(CT | \"computed tomography\") & (PE | \"pulmonary embolism\") & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressor septic shock", "completion": "vasopressor & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrinary biomarker AKI", "completion": "(urinary | uNGAL) & biomarker & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPEEP ARDS management", "completion": "(PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\") & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril valsartan HF", "completion": "(sacubitril valsartan | Entresto) & (HF | \"heart failure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet count preeclampsia", "completion": "(platelet | PLT) & count & preeclampsia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nINR measurement", "completion": "(INR | \"international normalized ratio\") & measurement"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal diagnosis diaphragmatic hernia", "completion": "(fetal | prenatal) & diagnosis & (\"diaphragmatic hernia\" | CDH)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic colorectal surgery", "completion": "robotic & (colorectal | colectomy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP anti-TNF response", "completion": "(CRP | \"C-reactive protein\") & (\"anti-TNF\" | biologics) & response"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDAPT duration after angioplasty", "completion": "duration & (DAPT | \"dual antiplatelet therapy\") & angioplasty"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin stop antibiotics", "completion": "procalcitonin & stop & antibiotics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProgesterone prevent PTB", "completion": "progesterone & prevent & (PTB | \"preterm birth\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSedation alcohol withdrawal syndrome", "completion": "sedation & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEchocardiography pericardial effusion", "completion": "(echocardiography | echo) & (\"pericardial effusion\" | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntra-articular steroid injection", "completion": "(intra-articular | IA) & steroid & injection"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLaparoscopic cholecystectomy acute", "completion": "laparoscopic & cholecystectomy & acute"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFerritin in HLH", "completion": "ferritin & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood pressure cuff accuracy", "completion": "(BP | \"blood pressure\") & cuff & accuracy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal ultrasound IUGR", "completion": "(fetal | prenatal) & ultrasound & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitor therapy", "completion": "(PCSK9 | inhibitor) & therapy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D for osteoporosis", "completion": "(vitamin D | cholecalciferol) & osteoporosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHyponatremia correction guidelines", "completion": "hyponatremia & correction & guidelines"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPercutaneous ASD closure", "completion": "percutaneous & (ASD | \"atrial septal defect\") & closure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotic therapy bacteremia", "completion": "antibiotics & therapy & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCMR for cardiac sarcoid", "completion": "(CMR | \"cardiac MRI\") & (\"cardiac sarcoid\" | sarcoidosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate as sepsis marker", "completion": "lactate & sepsis & marker"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid injection for CTS", "completion": "steroid & injection & (CTS | \"carpal tunnel syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood pressure in ICH", "completion": "(BP | \"blood pressure\") & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphopenia in COVID-19", "completion": "lymphopenia & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine for Graves' disease", "completion": "(radioiodine | I-131) & (\"Graves'' disease\" | hyperthyroidism)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG for superficial bladder cancer", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & (\"superficial bladder cancer\" | NMIBC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nClopidogrel after PCI", "completion": "(clopidogrel | Plavix) & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyroid FNA cytology", "completion": "thyroid & (FNA | \"fine needle aspiration\") & cytology"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine efficacy", "completion": "(liposomal | Exparel) & bupivacaine & efficacy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContrast nephropathy prevention", "completion": "(contrast | dye) & nephropathy & prevention"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWarfarin during pregnancy", "completion": "warfarin & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy in IBD", "completion": "(capsule | VCE) & endoscopy & (IBD | \"inflammatory bowel disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring anesthesia", "completion": "(BIS | \"bispectral index\") & monitoring & anesthesia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone adrenal insufficiency", "completion": "hydrocortisone & (\"adrenal insufficiency\" | Addison''s)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTracheal cuff pressure monitoring", "completion": "(tracheal | endotracheal) & cuff & pressure & monitoring"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for C. diff colitis", "completion": "antibiotics & (\"C. diff\" | CDI) & colitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic hemostasis techniques", "completion": "endoscopic & hemostasis & techniques"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCT diagnosis pulmonary embolism", "completion": "(CT | \"computed tomography\") & diagnosis & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressors in septic shock", "completion": "vasopressors & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNGAL for acute kidney injury", "completion": "(NGAL | \"neutrophil gelatinase-associated lipocalin\") & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPEEP in ARDS management", "completion": "(PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\") & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in heart failure", "completion": "(sacubitril/valsartan | Entresto) & (\"heart failure\" | HF)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet levels in HELLP", "completion": "(platelet | PLT) & levels & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nINR for anticoagulation", "completion": "(INR | \"international normalized ratio\") & anticoagulation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal imaging for CDH", "completion": "(fetal | prenatal) & imaging & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic surgery for colon cancer", "completion": "robotic & surgery & (\"colon cancer\" | colorectal)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP response to biologics", "completion": "(CRP | \"C-reactive protein\") & response & biologics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDAPT after coronary stenting", "completion": "(DAPT | \"dual antiplatelet therapy\") & (\"coronary stenting\" | stent)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin-guided antibiotic cessation", "completion": "(PCT | procalcitonin) & guided & antibiotics & cessation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVaginal progesterone for preterm birth prevention", "completion": "vaginal & progesterone & (\"preterm birth\" | PTB) & prevention"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSedation in alcohol withdrawal", "completion": "sedation & (\"alcohol withdrawal\" | AWS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEcho for pericardial effusion", "completion": "(echo | echocardiography) & (\"pericardial effusion\" | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid injection for knee OA", "completion": "steroid & injection & (knee | OA | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLaparoscopic cholecystectomy in acute cholecystitis", "completion": "laparoscopic & cholecystectomy & (\"acute cholecystitis\" | gallbladder)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFerritin level in HLH", "completion": "ferritin & level & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff size for blood pressure measurement", "completion": "cuff & size & (BP | \"blood pressure\") & measurement"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltrasound assessment of fetal growth", "completion": "ultrasound & assessment & fetal & growth"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors for hypercholesterolemia", "completion": "(PCSK9 | inhibitor) & hypercholesterolemia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D supplementation for osteoporosis", "completion": "(vitamin D | cholecalciferol) & supplementation & osteoporosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHyponatremia management guidelines", "completion": "hyponatremia & management & guidelines"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPercutaneous closure of ASD", "completion": "percutaneous & closure & (ASD | \"atrial septal defect\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotic choice in bacteremia", "completion": "antibiotics & choice & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI for myocarditis", "completion": "(CMR | \"cardiac MRI\") & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate as prognostic marker in sepsis", "completion": "lactate & prognostic & marker & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid injection for carpal tunnel syndrome", "completion": "(corticosteroid | steroid) & injection & (\"carpal tunnel syndrome\" | CTS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlood pressure control in ICH", "completion": "(BP | \"blood pressure\") & control & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphocyte count in COVID", "completion": "lymphocyte & count & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nI-131 therapy for hyperthyroidism", "completion": "(I-131 | radioiodine) & therapy & hyperthyroidism"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG treatment for bladder cancer", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & treatment & (\"bladder cancer\" | TCC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nClopidogrel after coronary stent", "completion": "(clopidogrel | Plavix) & (\"coronary stent\" | PCI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFNA of thyroid nodules", "completion": "(FNA | \"fine needle aspiration\") & (\"thyroid nodules\" | nodules)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in surgical pain", "completion": "(liposomal | Exparel) & bupivacaine & (surgical | pain)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPrevention of contrast nephropathy", "completion": "prevention & (contrast | dye) & nephropathy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWarfarin use in pregnancy", "completion": "warfarin & use & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy for small bowel Crohn's", "completion": "(capsule | VCE) & endoscopy & (\"small bowel\" | SB) & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS for anesthetic depth", "completion": "(BIS | \"bispectral index\") & (anesthetic | anesthesia) & depth"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGlucocorticoid replacement for adrenal insufficiency", "completion": "(glucocorticoid | hydrocortisone) & replacement & (\"adrenal insufficiency\" | Addison''s)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndotracheal tube cuff pressure management", "completion": "(endotracheal | ET) & tube & cuff & pressure & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTreatment of C. difficile infection", "completion": "treatment & (\"C. difficile\" | CDI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic techniques for GI bleeding", "completion": "endoscopic & techniques & (\"GI bleeding\" | hemorrhage)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCT for diagnosis of PE", "completion": "(CT | \"computed tomography\") & diagnosis & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressor selection in septic shock", "completion": "(vasopressor | pressor) & selection & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrinary NGAL for AKI", "completion": "(urinary | uNGAL) & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimizing PEEP in ARDS", "completion": "(PEEP | \"positive end-expiratory pressure\") & optimizing & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril valsartan for HF", "completion": "(sacubitril valsartan | Entresto) & (HF | \"heart failure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThrombocytopenia in HELLP syndrome", "completion": "thrombocytopenia & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMonitoring INR on warfarin", "completion": "monitoring & (INR | \"international ratio\") & warfarin"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPrenatal diagnosis of CDH", "completion": "(prenatal | fetal) & diagnosis & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic-assisted colectomy for cancer", "completion": "(robotic-assisted | robotic) & colectomy & cancer"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP as biomarker for anti-TNF response", "completion": "(CRP | \"C-reactive protein\") & biomarker & (\"anti-TNF\" | biologics) & response"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDuration of DAPT post-PCI", "completion": "duration & (DAPT | \"dual antiplatelet therapy\") & (post-PCI | \"after percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin to discontinue antibiotics", "completion": "procalcitonin & discontinue & antibiotics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProgesterone to prevent preterm delivery", "completion": "progesterone & prevent & (\"preterm delivery\" | PTB)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSedation protocols for AWS", "completion": "sedation & protocols & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEchocardiographic detection of pericardial effusion", "completion": "(echocardiographic | echo) & detection & (\"pericardial effusion\" | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntra-articular corticosteroids for osteoarthritis", "completion": "(intra-articular | IA) & corticosteroids & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTiming of laparoscopic cholecystectomy in acute setting", "completion": "timing & laparoscopic & cholecystectomy & acute"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFerritin as diagnostic marker for HLH", "completion": "ferritin & diagnostic & marker & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nImpact of cuff size on BP accuracy", "completion": "cuff & size & (BP | \"blood pressure\") & accuracy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltrasound evaluation of fetal growth restriction", "completion": "ultrasound & evaluation & (\"fetal growth restriction\" | IUGR)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibition for LDL reduction", "completion": "(PCSK9 | inhibitor) & (LDL | cholesterol) & reduction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D in osteoporosis management", "completion": "(vitamin D | cholecalciferol) & osteoporosis & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGuidelines for hyponatremia correction", "completion": "guidelines & hyponatremia & correction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTranscatheter ASD closure outcomes", "completion": "transcatheter & (ASD | \"atrial septal defect\") & closure & outcomes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEmpiric antibiotic selection in bacteremia", "completion": "(empiric | antibiotics) & selection & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI accuracy for myocarditis", "completion": "(CMR | \"cardiac MRI\") & accuracy & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPrognostic value of lactate in sepsis", "completion": "(prognostic | value) & lactate & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEfficacy of steroid injections for carpal tunnel", "completion": "efficacy & steroid & injections & (\"carpal tunnel\" | CTS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal BP targets in ICH", "completion": "(BP | \"blood pressure\") & targets & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphopenia as predictor in COVID", "completion": "lymphopenia & predictor & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine treatment efficacy in Graves' disease", "completion": "(radioiodine | I-131) & efficacy & (\"Graves'' disease\" | hyperthyroidism)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG immunotherapy efficacy in bladder cancer", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & immunotherapy & efficacy & (\"bladder cancer\" | TCC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nClopidogrel duration after drug-eluting stent", "completion": "(clopidogrel | Plavix) & duration & (DES | \"drug-eluting stent\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDiagnostic accuracy of thyroid FNA", "completion": "diagnostic & accuracy & thyroid & (FNA | \"fine needle aspiration\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPain control with liposomal bupivacaine", "completion": "pain & control & (liposomal | Exparel) & bupivacaine"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nStrategies to prevent contrast-induced nephropathy", "completion": "strategies & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRisks of warfarin during pregnancy", "completion": "risks & warfarin & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy diagnostic yield in small bowel Crohn's", "completion": "(capsule | VCE) & endoscopy & (diagnostic | yield) & (\"small bowel\" | SB) & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring to prevent awareness during anesthesia", "completion": "(BIS | \"bispectral index\") & monitoring & prevention & awareness & anesthesia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone dosing in adrenal crisis", "completion": "hydrocortisone & dosing & (\"adrenal crisis\" | insufficiency)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndotracheal cuff pressure complications", "completion": "(endotracheal | ET) & cuff & pressure & complications"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFirst-line treatment for C. diff colitis", "completion": "(first-line | 1L) & treatment & (\"C. diff\" | CDI) & colitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAdvanced endoscopic hemostasis techniques", "completion": "advanced & endoscopic & hemostasis & techniques"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSensitivity of CT angiography for PE", "completion": "sensitivity & (CTA | \"CT angiography\") & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNorepinephrine vs vasopressin in septic shock", "completion": "(norepinephrine | noradrenaline) & (vasopressin | ADH) & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPredictive value of urinary NGAL for AKI", "completion": "(predictive | value) & (urinary | uNGAL) & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIndividualized PEEP setting in ARDS", "completion": "individualized & (PEEP | \"positive end-expiratory pressure\") & setting & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMortality benefit of sacubitril/valsartan in HF", "completion": "mortality & benefit & (sacubitril/valsartan | Entresto) & (HF | \"heart failure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion thresholds in HELLP", "completion": "(platelet | PLT) & transfusion & thresholds & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFrequency of INR monitoring", "completion": "frequency & (INR | \"international ratio\") & monitoring"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAccuracy of fetal MRI for CDH", "completion": "accuracy & (fetal | prenatal) & (MRI | \"magnetic resonance\") & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOncologic outcomes of robotic colectomy", "completion": "oncologic & outcomes & robotic & colectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP as predictor of response to TNF inhibitors", "completion": "(CRP | \"C-reactive protein\") & predictor & response & (\"TNF inhibitors\" | biologics)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of DAPT after DES", "completion": "duration & (DAPT | \"dual antiplatelet therapy\") & (DES | \"drug-eluting stent\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin to guide antibiotic duration", "completion": "procalcitonin & guide & antibiotics & duration"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProgesterone formulations for preterm prevention", "completion": "progesterone & formulations & prevention & preterm"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBenzodiazepine-based sedation for AWS", "completion": "(benzodiazepine | BZD) & sedation & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nQuantification of pericardial effusion by echo", "completion": "quantification & (\"pericardial effusion\" | fluid) & (echo | echocardiography)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSteroid injections vs surgery for carpal tunnel", "completion": "steroid & injections & surgery & (\"carpal tunnel\" | CTS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEarly vs delayed cholecystectomy in mild pancreatitis", "completion": "(early | delayed) & cholecystectomy & mild & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDiagnostic criteria for HLH including ferritin", "completion": "(diagnostic | criteria) & (HLH | \"hemophagocytic lymphohistiocytosis\") & ferritin"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEffect of cuff size on hypertension diagnosis", "completion": "cuff & size & (hypertension | BP) & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltrasound predictors of adverse outcome in IUGR", "completion": "ultrasound & predictors & (adverse | outcome) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCost-effectiveness of PCSK9 inhibitors", "completion": "(cost-effectiveness | economic) & (PCSK9 | inhibitor)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and calcium for osteoporosis prevention", "completion": "(vitamin D | cholecalciferol) & calcium & osteoporosis & prevention"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRapid vs slow correction of hyponatremia", "completion": "(rapid | slow) & correction & hyponatremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLong-term outcomes of percutaneous ASD closure", "completion": "\"long term\" & outcomes & percutaneous & (ASD | \"atrial septal defect\") & closure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAntibiotic resistance in Gram-negative bacteremia", "completion": "(antibiotic | resistance) & (\"Gram-negative\" | bacteremia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nT1 and T2 mapping in cardiac MRI for myocarditis", "completion": "(T1 | T2) & mapping & (CMR | \"cardiac MRI\") & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance as goal in sepsis resuscitation", "completion": "(lactate | clearance) & goal & sepsis & resuscitation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltrasound-guided vs landmark steroid injection for CTS", "completion": "(ultrasound-guided | landmark) & steroid & injection & (CTS | \"carpal tunnel syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntensive vs conventional BP control in ICH", "completion": "(intensive | conventional) & (BP | \"blood pressure\") & control & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLymphocyte-to-CRP ratio in COVID prognosis", "completion": "(lymphocyte | CRP) & ratio & (COVID | SARS-CoV-2) & prognosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine vs antithyroid drugs for Graves' disease", "completion": "(radioiodine | I-131) & (antithyroid | methimazole) & (\"Graves'' disease\" | hyperthyroidism)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG maintenance therapy for high-risk NMIBC", "completion": "(BCG | \"Bacillus Calmette-Gu\u00e9rin\") & maintenance & therapy & (\"high risk\" | high-risk) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor vs clopidogrel after acute coronary syndrome", "completion": "(ticagrelor | Brilinta) & (clopidogrel | Plavix) & (ACS | \"acute coronary syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMolecular testing of thyroid FNA samples", "completion": "(molecular | genetic) & testing & thyroid & (FNA | \"fine needle aspiration\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in enhanced recovery after surgery", "completion": "(liposomal | Exparel) & bupivacaine & (\"enhanced recovery\" | ERAS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSodium bicarbonate for contrast nephropathy prevention", "completion": "(sodium bicarbonate | NaHCO3) & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLMWH vs warfarin in pregnancy", "completion": "(LMWH | \"low molecular weight heparin\") & warfarin & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy vs MRE for small bowel Crohn's", "completion": "(capsule | VCE) & (MRE | \"magnetic resonance enterography\") & (\"small bowel\" | SB) & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring and postoperative cognitive dysfunction", "completion": "(BIS | \"bispectral index\") & monitoring & (POCD | \"postoperative cognitive dysfunction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nStress-dose steroids in septic shock with adrenal insufficiency", "completion": "(stress-dose | steroids) & (\"septic shock\" | sepsis) & (\"adrenal insufficiency\" | cortisol)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutomated cuff pressure controllers in mechanical ventilation", "completion": "automated & cuff & pressure & controllers & (\"mechanical ventilation\" | MV)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal microbiota transplantation for recurrent C. diff", "completion": "(FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (\"C. diff\" | CDI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOver-the-scope clips for GI bleeding", "completion": "(OTSC | \"over-the-scope clips\") & (\"GI bleeding\" | hemorrhage)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer combined with clinical probability for PE", "completion": "(D-dimer | \"fibrin fragment\") & (\"clinical probability\" | Wells) & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II for vasodilatory shock", "completion": "(angiotensin II | Giapreza) & (vasodilatory | septic) & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCombination biomarkers for AKI prediction", "completion": "combination & biomarkers & (AKI | \"acute kidney injury\") & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEsophageal pressure-guided PEEP in ARDS", "completion": "(esophageal | pressure) & guided & (PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in HF with preserved ejection fraction", "completion": "(sacubitril/valsartan | Entresto) & (HFpEF | \"heart failure with preserved ejection fraction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet count recovery after delivery in HELLP", "completion": "(platelet | PLT) & count & recovery & delivery & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPoint-of-care INR testing", "completion": "(point-of-care | POC) & (INR | \"international ratio\") & testing"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI predictors of outcome in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & predictors & outcome & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic vs laparoscopic colectomy for cancer", "completion": "(robotic | laparoscopic) & colectomy & cancer"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSerial CRP measurements to monitor biologic response", "completion": "(serial | repeated) & (CRP | \"C-reactive protein\") & measurements & monitor & (biologics | response)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nShort vs long DAPT after high-bleeding-risk PCI", "completion": "(short | long) & (DAPT | \"dual antiplatelet therapy\") & (\"high bleeding risk\" | HBR) & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics to predict antibiotic failure", "completion": "(PCT | procalcitonin) & kinetics & predict & antibiotic & failure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nComparative effectiveness of progesterone formulations for PTB", "completion": "(comparative | effectiveness) & progesterone & formulations & (PTB | \"preterm birth\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPhenobarbital vs benzodiazepines for AWS", "completion": "(phenobarbital | barbiturate) & (benzodiazepines | BZD) & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D echocardiography for pericardial effusion", "completion": "(3D | \"three dimensional\") & echocardiography & (\"pericardial effusion\" | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet-rich plasma vs steroid injection for knee OA", "completion": "(PRP | \"platelet rich plasma\") & steroid & injection & (knee | OA | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy for mild gallstone pancreatitis", "completion": "(same-admission | immediate) & cholecystectomy & mild & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore for HLH diagnosis", "completion": "(HScore | \"HScore\") & (HLH | \"hemophagocytic lymphohistiocytosis\") & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff size effect on masked hypertension", "completion": "cuff & size & effect & (\"masked hypertension\" | BP)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoppler ultrasound parameters in fetal growth restriction", "completion": "(Doppler | ultrasound) & parameters & (\"fetal growth restriction\" | IUGR)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in familial hypercholesterolemia", "completion": "(PCSK9 | inhibitor) & (FH | \"familial hypercholesterolemia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and fracture risk in elderly", "completion": "(vitamin D | cholecalciferol) & (fracture | risk) & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressin vs norepinephrine in hyponatremic shock", "completion": "(vasopressin | ADH) & (norepinephrine | noradrenaline) & (hyponatremic | shock)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMinimally invasive ASD closure techniques", "completion": "(minimally invasive | percutaneous) & (ASD | \"atrial septal defect\") & closure & techniques"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRapid diagnostics in Gram-negative bacteremia", "completion": "(rapid | diagnostics) & (\"Gram-negative\" | bacteremia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nT1 mapping cutoff values in acute myocarditis", "completion": "(T1 | mapping) & (cutoff | values) & acute & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate-directed resuscitation in sepsis", "completion": "(lactate | clearance) & directed & resuscitation & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCost-effectiveness of steroid injections for CTS", "completion": "(cost-effectiveness | economic) & steroid & injections & (CTS | \"carpal tunnel syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal systolic BP target in ICH evacuation", "completion": "(systolic | BP) & target & (ICH | \"intracerebral hemorrhage\") & evacuation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMachine learning models using lymphocyte data for COVID prognosis", "completion": "\"machine learning\" & models & lymphocyte & data & (COVID | SARS-CoV-2) & prognosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nQuality of life after radioiodine for Graves' disease", "completion": "(QoL | \"quality of life\") & after & (radioiodine | I-131) & (\"Graves'' disease\" | hyperthyroidism)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBCG-unresponsive NMIBC treatment options", "completion": "(BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\") & treatment"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDe-escalation from ticagrelor to clopidogrel after ACS", "completion": "(de-escalation | switch) & (ticagrelor | Brilinta) & (clopidogrel | Plavix) & (ACS | \"acute coronary syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyroSeq molecular testing for indeterminate thyroid nodules", "completion": "(ThyroSeq | molecular) & testing & (indeterminate | Bethesda) & (\"thyroid nodules\" | nodules)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in outpatient surgery", "completion": "(liposomal | Exparel) & bupivacaine & (outpatient | ambulatory) & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nStatin pretreatment for contrast nephropathy prevention", "completion": "(statin | HMG-CoA) & pretreatment & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDirect oral anticoagulants vs LMWH in pregnancy", "completion": "(DOACs | \"direct oral anticoagulants\") & (LMWH | \"low molecular weight heparin\") & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nArtificial intelligence in capsule endoscopy for Crohn's", "completion": "(AI | \"artificial intelligence\") & (capsule | VCE) & endoscopy & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS-guided anesthesia and long-term cancer outcomes", "completion": "(BIS | \"bispectral index\") & guided & anesthesia & (\"long term\" | survival) & cancer"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid dosing in septic shock with critical illness-related corticosteroid insufficiency", "completion": "(corticosteroid | hydrocortisone) & dosing & (\"septic shock\" | sepsis) & (CIRCI | \"critical illness-related corticosteroid insufficiency\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff pressure variability during patient transport", "completion": "cuff & pressure & variability & (\"patient transport\" | transport)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFrozen vs fresh FMT for recurrent C. diff", "completion": "(frozen | fresh) & (FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (\"C. diff\" | CDI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHemospray vs conventional methods for peptic ulcer bleeding", "completion": "(Hemospray | TC-325) & (conventional | methods) & (\"peptic ulcer\" | ulcer) & bleeding"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAge-adjusted D-dimer for PE exclusion in elderly", "completion": "(age-adjusted | modified) & (D-dimer | \"fibrin fragment\") & (PE | \"pulmonary embolism\") & exclusion & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in high-output heart failure", "completion": "(angiotensin II | Giapreza) & (\"high output\" | HF) & (\"heart failure\" | HF)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTIMP-2\u2022IGFBP7 for AKI risk stratification", "completion": "(TIMP-2\u2022IGFBP7 | NephroCheck) & (AKI | \"acute kidney injury\") & (risk | stratification)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIndividualized PEEP using electrical impedance tomography", "completion": "individualized & (PEEP | \"positive end-expiratory pressure\") & (EIT | \"electrical impedance tomography\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in HF with mid-range EF", "completion": "(sacubitril/valsartan | Entresto) & (HFmrEF | \"heart failure with mid-range ejection fraction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPlatelet function testing in HELLP syndrome", "completion": "(platelet | function) & testing & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPatient self-testing of INR", "completion": "(patient | self) & testing & (INR | \"international ratio\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume quantification in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume\" | quantification) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic surgery learning curve for colectomy", "completion": "robotic & surgery & (\"learning curve\" | training) & colectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP velocity to predict anti-TNF primary non-response", "completion": "(CRP | \"C-reactive protein\") & velocity & predict & (\"anti-TNF\" | biologics) & (\"non response\" | failure)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGenotype-guided DAPT duration after PCI", "completion": "(genotype | CYP2C19) & guided & (DAPT | \"dual antiplatelet therapy\") & duration & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin change to guide antibiotic therapy in sepsis", "completion": "(PCT | procalcitonin) & change & guide & antibiotics & therapy & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVaginal progesterone vs cerclage for preterm birth prevention", "completion": "vaginal & progesterone & (cerclage | suture) & prevention & (\"preterm birth\" | PTB)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine adjunct for severe AWS", "completion": "ketamine & adjunct & severe & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D echo guidance for pericardiocentesis", "completion": "(3D | \"three dimensional\") & (echo | echocardiography) & guidance & pericardiocentesis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPRP injection for thumb base osteoarthritis", "completion": "(PRP | \"platelet rich plasma\") & injection & (\"thumb base\" | CMC) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPredictive factors for choledocholithiasis in mild gallstone pancreatitis", "completion": "predictive & factors & (choledocholithiasis | stones) & mild & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore validation in adult HLH", "completion": "(HScore | \"HScore\") & validation & (adult | HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEffect of cuff size on nocturnal hypertension", "completion": "cuff & size & effect & (nocturnal | hypertension)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUmbilical artery Doppler in early-onset IUGR", "completion": "(umbilical | UA) & Doppler & (\"early onset\" | early) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in statin intolerance", "completion": "(PCSK9 | inhibitor) & (statin | HMG-CoA) & intolerance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and falls in elderly", "completion": "(vitamin D | cholecalciferol) & falls & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVasopressin in catecholamine-resistant septic shock", "completion": "(vasopressin | ADH) & (catecholamine-resistant | refractory) & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDevice closure vs surgery for ASD", "completion": "(device | percutaneous) & closure & surgery & (ASD | \"atrial septal defect\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRapid AST in bloodstream infections", "completion": "(rapid | accelerated) & (AST | \"antimicrobial susceptibility testing\") & (\"bloodstream infections\" | bacteremia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nExtracellular volume fraction in chronic myocarditis", "completion": "(ECV | \"extracellular volume\") & fraction & chronic & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance vs ScvO2 in sepsis resuscitation", "completion": "(lactate | clearance) & (ScvO2 | \"central venous oxygen saturation\") & sepsis & resuscitation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCost-utility analysis of steroid injections for CTS", "completion": "(cost-utility | economic) & analysis & steroid & injections & (CTS | \"carpal tunnel syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP variability in ICH outcome", "completion": "(systolic | BP) & variability & (ICH | \"intracerebral hemorrhage\") & outcome"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDeep learning using lymphocyte images for COVID severity", "completion": "\"deep learning\" & lymphocyte & images & (COVID | SARS-CoV-2) & severity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine vs surgery for toxic adenoma", "completion": "(radioiodine | I-131) & surgery & (\"toxic adenoma\" | nodule)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntravesical gemcitabine/docetaxel for BCG-unresponsive NMIBC", "completion": "(intravesical | bladder) & (gemcitabine/docetaxel | chemotherapy) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDe-escalation DAPT strategies after ACS", "completion": "de-escalation & (DAPT | \"dual antiplatelet therapy\") & strategies & (ACS | \"acute coronary syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMolecular classifier for indeterminate thyroid cytology", "completion": "(molecular | classifier) & (indeterminate | Bethesda) & thyroid & cytology"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in ERAS protocols", "completion": "(liposomal | Exparel) & bupivacaine & (ERAS | \"enhanced recovery after surgery\") & protocols"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRemote ischemic preconditioning for contrast nephropathy", "completion": "(remote | ischemic) & preconditioning & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDOACs in mechanical heart valves", "completion": "(DOACs | \"direct oral anticoagulants\") & (\"mechanical heart valves\" | MHV)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAI-assisted reading of capsule endoscopy in Crohn's", "completion": "(AI | \"artificial intelligence\") & assisted & reading & (capsule | VCE) & endoscopy & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring and delirium after cardiac surgery", "completion": "(BIS | \"bispectral index\") & monitoring & (delirium | confusion) & (\"cardiac surgery\" | CABG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid tapering in septic shock recovery", "completion": "(corticosteroid | steroid) & tapering & (\"septic shock\" | sepsis) & recovery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContinuous cuff pressure control systems", "completion": "continuous & cuff & pressure & control & systems"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOral vs encapsulated FMT for recurrent CDI", "completion": "(oral | encapsulated) & (FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (CDI | \"C. diff infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHemostatic powder vs clip for malignant GI bleeding", "completion": "(hemostatic | powder) & (clip | endoclip) & (malignant | cancer) & (\"GI bleeding\" | hemorrhage)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nYEARS algorithm for PE diagnosis", "completion": "(YEARS | algorithm) & (PE | \"pulmonary embolism\") & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in distributive shock", "completion": "(angiotensin II | Giapreza) & (distributive | vasodilatory) & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI prediction in sepsis", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & prediction & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT-guided PEEP titration in ARDS", "completion": "(EIT | \"electrical impedance tomography\") & guided & (PEEP | \"positive end-expiratory pressure\") & titration & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in cancer therapy-related cardiac dysfunction", "completion": "(sacubitril/valsartan | Entresto) & (CTRCD | \"cancer therapy-related cardiac dysfunction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThrombopoietin agonists in HELLP syndrome", "completion": "(thrombopoietin | TPO) & agonists & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPharmacist-managed INR clinics", "completion": "pharmacist & managed & (INR | \"international ratio\") & clinics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI liver position in CDH prognosis", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"liver position\" | herniation) & prognosis & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic colectomy cost-effectiveness", "completion": "robotic & colectomy & (cost-effectiveness | economic)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP-based treat-to-target in IBD", "completion": "(CRP | \"C-reactive protein\") & (\"treat to target\" | T2T) & (IBD | \"inflammatory bowel disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 genotype and DAPT outcomes", "completion": "(CYP2C19 | genotype) & (DAPT | \"dual antiplatelet therapy\") & outcomes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin slope for antibiotic stewardship", "completion": "(PCT | procalcitonin) & slope & (antibiotic | stewardship)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n17-OHPC vs vaginal progesterone for PTB prevention", "completion": "(17-OHPC | \"hydroxyprogesterone caproate\") & vaginal & progesterone & prevention & (PTB | \"preterm birth\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDexmedetomidine for AWS with delirium", "completion": "(dexmedetomidine | Precedex) & (AWS | \"alcohol withdrawal syndrome\") & delirium"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D echo vs CT for pericardial effusion characterization", "completion": "(3D | \"three dimensional\") & (echo | echocardiography) & (CT | \"computed tomography\") & (\"pericardial effusion\" | fluid) & characterization"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPRP vs hyaluronic acid for knee OA", "completion": "(PRP | \"platelet rich plasma\") & (hyaluronic | HA) & (knee | OA | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPredictive model for choledocholithiasis in acute biliary pancreatitis", "completion": "predictive & model & (choledocholithiasis | stones) & acute & (biliary | gallstone) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in secondary HLH", "completion": "(HScore | \"HScore\") & (secondary | HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff size impact on hypertension prevalence", "completion": "cuff & size & impact & (hypertension | prevalence)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDuctus venosus Doppler in early IUGR", "completion": "(ductus venosus | DV) & Doppler & (early | onset) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in heterozygous FH", "completion": "(PCSK9 | inhibitor) & (heterozygous | HeFH) & (FH | \"familial hypercholesterolemia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and muscle strength in elderly", "completion": "(vitamin D | cholecalciferol) & (muscle | strength) & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelepressin vs norepinephrine in septic shock", "completion": "(selepressin | vasopressin) & (norepinephrine | noradrenaline) & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAmplatzer vs Gore device for ASD closure", "completion": "(Amplatzer | Gore) & device & (ASD | \"atrial septal defect\") & closure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRapid multiplex PCR for bloodstream infections", "completion": "(rapid | multiplex) & (PCR | \"polymerase chain reaction\") & (\"bloodstream infections\" | bacteremia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNative T1 mapping in acute myocarditis diagnosis", "completion": "(native | T1) & mapping & acute & myocarditis & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time in septic shock", "completion": "(lactate | clearance) & time & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMarkov model cost-effectiveness of CTS treatments", "completion": "(Markov | model) & (cost-effectiveness | economic) & (CTS | \"carpal tunnel syndrome\") & treatments"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOptimal BP trajectory in ICH recovery", "completion": "(BP | \"blood pressure\") & trajectory & (ICH | \"intracerebral hemorrhage\") & recovery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nConvolutional neural network for lymphocyte classification in COVID", "completion": "(CNN | \"convolutional neural network\") & lymphocyte & classification & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine ablation vs antithyroid drugs for toxic MNG", "completion": "(radioiodine | I-131) & ablation & (antithyroid | methimazole) & (\"toxic MNG\" | multinodular)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNadofaragene firadenovec for BCG-unresponsive NMIBC", "completion": "(nadofaragene | firadenovec) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after short DAPT", "completion": "(ticagrelor | Brilinta) & monotherapy & (short | brief) & (DAPT | \"dual antiplatelet therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAfirma GSC for indeterminate thyroid nodules", "completion": "(Afirma | GSC) & (indeterminate | Bethesda) & (\"thyroid nodules\" | nodules)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in total joint arthroplasty", "completion": "(liposomal | Exparel) & bupivacaine & (TJA | \"total joint arthroplasty\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTheophylline for contrast nephropathy prevention", "completion": "(theophylline | aminophylline) & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDOAC reversal agents in bleeding", "completion": "(DOACs | \"direct oral anticoagulants\") & (reversal | antidote) & bleeding"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDeep learning for small bowel lesion detection in capsule endoscopy", "completion": "\"deep learning\" & (\"small bowel\" | SB) & lesion & detection & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring and postoperative nausea", "completion": "(BIS | \"bispectral index\") & monitoring & (PONV | \"postoperative nausea and vomiting\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid duration in septic shock", "completion": "(corticosteroid | hydrocortisone) & duration & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutomated vs manual cuff pressure control", "completion": "(automated | manual) & cuff & pressure & control"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT delivery methods for recurrent CDI", "completion": "(FMT | \"fecal microbiota transplantation\") & (delivery | methods) & (recurrent | relapse) & (CDI | \"C. diff infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCombination endoscopic therapy for peptic ulcer bleeding", "completion": "combination & endoscopic & therapy & (\"peptic ulcer\" | ulcer) & bleeding"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPEGeD algorithm for PE diagnosis", "completion": "(PEGeD | algorithm) & (PE | \"pulmonary embolism\") & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in vasoplegia after cardiac surgery", "completion": "(angiotensin II | Giapreza) & (vasoplegia | shock) & (\"cardiac surgery\" | CABG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrinary CCL14 for persistent AKI", "completion": "(urinary | CCL14) & (persistent | severe) & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP optimization in obese ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & optimization & (obese | obesity) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in anthracycline-induced cardiotoxicity", "completion": "(sacubitril/valsartan | Entresto) & (anthracycline | doxorubicin) & cardiotoxicity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRomiplostim in HELLP syndrome", "completion": "(romiplostim | Nplate) & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTelemedicine for INR management", "completion": "telemedicine & (INR | \"international ratio\") & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI O/E LHR in CDH survival", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"O/E LHR\" | \"observed to expected lung area to head circumference ratio\") & survival & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLearning curve robotic colectomy", "completion": "(learning curve | training) & robotic & colectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP correlation with endoscopic activity in Crohn's", "completion": "(CRP | \"C-reactive protein\") & correlation & (endoscopic | activity) & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPharmacogenomic-guided DAPT after PCI", "completion": "(pharmacogenomic | genotype) & guided & (DAPT | \"dual antiplatelet therapy\") & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics in COVID pneumonia", "completion": "(PCT | procalcitonin) & kinetics & (COVID | SARS-CoV-2) & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCombined progesterone and cerclage for PTB prevention", "completion": "(combined | combination) & progesterone & cerclage & prevention & (PTB | \"preterm birth\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPhenobarbital protocol for severe AWS", "completion": "(phenobarbital | barbiturate) & protocol & severe & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D echo guidance vs fluoroscopy for pericardiocentesis", "completion": "(3D | \"three dimensional\") & (echo | echocardiography) & guidance & (fluoroscopy | X-ray) & pericardiocentesis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPRP injection for erosive hand OA", "completion": "(PRP | \"platelet rich plasma\") & injection & (erosive | hand) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPredictive score for ERCP need in gallstone pancreatitis", "completion": "predictive & score & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & need & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHLH-2004 vs HScore diagnostic accuracy", "completion": "(HLH-2004 | HScore) & (diagnostic | accuracy) & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCuff size and masked uncontrolled hypertension", "completion": "cuff & size & (\"masked uncontrolled\" | hypertension)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCerebroplacental ratio in late-onset IUGR", "completion": "(cerebroplacental | CPR) & ratio & (\"late onset\" | late) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in homozygous FH", "completion": "(PCSK9 | inhibitor) & (homozygous | HoFH) & (FH | \"familial hypercholesterolemia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and sarcopenia in elderly", "completion": "(vitamin D | cholecalciferol) & sarcopenia & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs norepinephrine in hepatorenal syndrome", "completion": "(terlipressin | vasopressin) & (norepinephrine | noradrenaline) & (\"hepatorenal syndrome\" | HRS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDevice closure for secundum ASD", "completion": "(device | percutaneous) & closure & (\"secundum ASD\" | ASD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFilmArray BCID for rapid pathogen identification", "completion": "(FilmArray | BCID) & (rapid | pathogen) & identification & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nT1 mapping in chronic inflammatory cardiomyopathy", "completion": "(T1 | mapping) & chronic & (inflammatory | myocarditis) & cardiomyopathy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio in sepsis mortality", "completion": "(lactate/albumin | ratio) & sepsis & mortality"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDiscrete event simulation for CTS treatment pathways", "completion": "(DES | \"discrete event simulation\") & (CTS | \"carpal tunnel syndrome\") & (treatment | pathways)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBP variability and hematoma expansion in ICH", "completion": "(BP | \"blood pressure\") & variability & (\"hematoma expansion\" | growth) & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTransfer learning for COVID lymphocyte analysis", "completion": "\"transfer learning\" & (COVID | SARS-CoV-2) & lymphocyte & analysis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine vs surgery for toxic nodular goiter", "completion": "(radioiodine | I-131) & surgery & (\"toxic nodular goiter\" | MNG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntravesical nadofaragene for high-risk NMIBC", "completion": "(intravesical | bladder) & (nadofaragene | firadenovec) & (\"high risk\" | high-risk) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after 1-month DAPT", "completion": "(ticagrelor | Brilinta) & monotherapy & (\"1 month\" | short) & (DAPT | \"dual antiplatelet therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyGeNEXT/ThyraMIR for indeterminate thyroid nodules", "completion": "(ThyGeNEXT | ThyraMIR) & (indeterminate | Bethesda) & (\"thyroid nodules\" | nodules)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in abdominal wall reconstruction", "completion": "(liposomal | Exparel) & bupivacaine & (\"abdominal wall\" | hernia) & reconstruction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAllopurinol for contrast nephropathy prevention", "completion": "(allopurinol | Zyloprim) & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAndexanet alfa for DOAC reversal", "completion": "(andexanet | Andexxa) & (DOACs | \"direct oral anticoagulants\") & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCNN for ulcer detection in capsule endoscopy", "completion": "(CNN | \"convolutional neural network\") & ulcer & detection & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring and opioid consumption", "completion": "(BIS | \"bispectral index\") & monitoring & (opioid | consumption)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid weaning in septic shock recovery", "completion": "(corticosteroid | hydrocortisone) & weaning & (\"septic shock\" | sepsis) & recovery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSmart cuff pressure regulation systems", "completion": "(smart | automated) & cuff & pressure & regulation & systems"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT via colonoscopy vs capsules for recurrent CDI", "completion": "(FMT | \"fecal microbiota transplantation\") & (colonoscopy | capsules) & (recurrent | relapse) & (CDI | \"C. diff infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOverStitch for refractory GI bleeding", "completion": "(OverStitch | endoscopic) & (refractory | resistant) & (\"GI bleeding\" | hemorrhage)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n4-level PE diagnostic algorithm", "completion": "(4-level | algorithm) & (PE | \"pulmonary embolism\") & diagnostic"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in high-dose vasopressor shock", "completion": "(angiotensin II | Giapreza) & (\"high dose\" | vasopressor) & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTIMP2\u2022IGFBP7 for AKI staging", "completion": "(TIMP2\u2022IGFBP7 | NephroCheck) & (AKI | \"acute kidney injury\") & staging"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for regional ventilation optimization in ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (regional | ventilation) & optimization & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in cardiotoxicity prevention", "completion": "(sacubitril/valsartan | Entresto) & (cardiotoxicity | prevention)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEltrombopag in HELLP syndrome", "completion": "(eltrombopag | Promacta) & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMobile app for INR self-management", "completion": "(mobile | app) & (INR | \"international ratio\") & (self-management | monitoring)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI total lung volume in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"total lung volume\" | TLV) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic colectomy for rectal cancer", "completion": "robotic & colectomy & (\"rectal cancer\" | rectum)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP-based de-escalation in Crohn's", "completion": "(CRP | \"C-reactive protein\") & (de-escalation | reduction) & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGenotype-guided vs standard DAPT", "completion": "(genotype | guided) & standard & (DAPT | \"dual antiplatelet therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin velocity in bacterial coinfection", "completion": "(PCT | procalcitonin) & velocity & (bacterial | coinfection)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n17-OHPC vs placebo for PTB prevention", "completion": "(17-OHPC | \"hydroxyprogesterone caproate\") & placebo & prevention & (PTB | \"preterm birth\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDexmedetomidine for AWS with benzodiazepine resistance", "completion": "(dexmedetomidine | Precedex) & (AWS | \"alcohol withdrawal syndrome\") & (resistance | refractory)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D echo vs MRI for pericardial effusion", "completion": "(3D | \"three dimensional\") & (echo | echocardiography) & (MRI | \"magnetic resonance\") & (\"pericardial effusion\" | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPRP injection for base of thumb OA", "completion": "(PRP | \"platelet rich plasma\") & injection & (\"base of thumb\" | CMC) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPredictive model for common bile duct stones in gallstone pancreatitis", "completion": "predictive & model & (\"common bile duct\" | CBD) & stones & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in macrophage activation syndrome", "completion": "(HScore | \"HScore\") & (MAS | \"macrophage activation syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLarge cuff effect on hypertension diagnosis rates", "completion": "(large | cuff) & size & effect & (hypertension | diagnosis) & rates"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUmbilical artery PI in IUGR management", "completion": "(umbilical | UA) & (PI | \"pulsatility index\") & (IUGR | \"intrauterine growth restriction\") & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in pediatric FH", "completion": "(PCSK9 | inhibitor) & pediatric & (FH | \"familial hypercholesterolemia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and physical performance in elderly", "completion": "(vitamin D | cholecalciferol) & (\"physical performance\" | function) & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelepressin in septic shock with ARDS", "completion": "(selepressin | vasopressin) & (\"septic shock\" | sepsis) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDevice closure for fenestrated Fontan", "completion": "(device | percutaneous) & closure & (\"fenestrated Fontan\" | Fontan)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nT2Bacteria for rapid bacteremia diagnosis", "completion": "(T2Bacteria | panel) & rapid & diagnosis & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV mapping in myocarditis", "completion": "(ECV | \"extracellular volume\") & mapping & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate/clearance ratio in septic shock", "completion": "(lactate | clearance) & ratio & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAgent-based modeling for CTS treatment access", "completion": "\"agent based modeling\" & (CTS | \"carpal tunnel syndrome\") & (treatment | access)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltra-early BP reduction in ICH", "completion": "(ultra-early | immediate) & (BP | \"blood pressure\") & reduction & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFederated learning for COVID lymphocyte data", "completion": "\"federated learning\" & (COVID | SARS-CoV-2) & lymphocyte & data"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine for toxic adenoma vs MNG", "completion": "(radioiodine | I-131) & (\"toxic adenoma\" | MNG | \"multinodular goiter\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCG0070 for BCG-unresponsive NMIBC", "completion": "(CG0070 | oncolytic) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPrasugrel monotherapy after PCI", "completion": "(prasugrel | Effient) & monotherapy & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRosettaGX Reveal for thyroid nodules", "completion": "(RosettaGX | Reveal) & (\"thyroid nodules\" | nodules)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in breast reconstruction", "completion": "(liposomal | Exparel) & bupivacaine & (\"breast reconstruction\" | mastectomy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNebivolol for contrast nephropathy", "completion": "(nebivolol | Bystolic) & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for DOAC reversal", "completion": "(ciraparantag | PER977) & (DOACs | \"direct oral anticoagulants\") & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVision transformer for capsule endoscopy analysis", "completion": "(ViT | \"vision transformer\") & (capsule | VCE) & endoscopy & analysis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS and postoperative cognitive function", "completion": "(BIS | \"bispectral index\") & (postoperative | cognitive) & function"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid duration impact on sepsis outcomes", "completion": "(corticosteroid | hydrocortisone) & duration & impact & (sepsis | outcomes)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nClosed-loop cuff pressure control", "completion": "(closed-loop | automated) & cuff & pressure & control"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT for recurrent CDI in IBD patients", "completion": "(FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (CDI | \"C. diff infection\") & (IBD | \"inflammatory bowel disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum therapy for GI perforations", "completion": "(endoscopic | vacuum) & therapy & (\"GI perforations\" | leak)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPregnancy-adapted YEARS algorithm", "completion": "(pregnancy | adapted) & (YEARS | algorithm) & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in burn shock", "completion": "(angiotensin II | Giapreza) & (\"burn shock\" | burns)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrinary CCL14 for AKI progression", "completion": "(urinary | CCL14) & (AKI | \"acute kidney injury\") & progression"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for personalized PEEP in ARDS", "completion": "(EIT | \"electrical impedance tomography\") & personalized & (PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in HER2-positive cardiotoxicity", "completion": "(sacubitril/valsartan | Entresto) & (HER2 | trastuzumab) & cardiotoxicity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag in HELLP syndrome", "completion": "(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWearable INR monitors", "completion": "(wearable | device) & (INR | \"international ratio\") & monitors"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI quantifies liver herniation in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & quantifies & (\"liver herniation\" | herniation) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic vs open rectal cancer surgery", "completion": "(robotic | open) & (\"rectal cancer\" | rectum) & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCRP-guided anti-TNF dosing in Crohn's", "completion": "(CRP | \"C-reactive protein\") & guided & (\"anti-TNF\" | biologics) & dosing & (\"Crohn''s disease\" | CD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing cost-effectiveness in PCI", "completion": "(CYP2C19 | genotype) & testing & (cost-effectiveness | economic) & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin half-life in COVID-19 ARDS", "completion": "(PCT | procalcitonin) & (\"half life\" | kinetics) & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCombined screening for PTB prevention", "completion": "combined & screening & prevention & (PTB | \"preterm birth\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine infusion for refractory AWS", "completion": "ketamine & infusion & (refractory | resistant) & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D echo guidance for complex pericardial effusions", "completion": "(3D | \"three dimensional\") & (echo | echocardiography) & guidance & (complex | loculated) & (\"pericardial effusions\" | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPRP injection for first CMC joint OA", "completion": "(PRP | \"platelet rich plasma\") & injection & (\"first CMC joint\" | thumb) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCholangitis prediction in gallstone pancreatitis", "completion": "cholangitis & prediction & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in rheumatology patients", "completion": "(HScore | \"HScore\") & (rheumatology | autoimmune)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThigh cuff for BP measurement in obesity", "completion": "(thigh | cuff) & (BP | \"blood pressure\") & measurement & obesity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMiddle cerebral artery Doppler in late IUGR", "completion": "(MCA | \"middle cerebral artery\") & Doppler & (late | onset) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in children with FH", "completion": "(PCSK9 | inhibitor) & children & (FH | \"familial hypercholesterolemia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and frailty in elderly", "completion": "(vitamin D | cholecalciferol) & frailty & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for hepatorenal syndrome type 1", "completion": "(terlipressin | vasopressin) & (\"hepatorenal syndrome\" | HRS) & type"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDevice closure for PFO with cryptogenic stroke", "completion": "(device | percutaneous) & closure & (PFO | \"patent foramen ovale\") & (\"cryptogenic stroke\" | stroke)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAccelerate Pheno for bloodstream infections", "completion": "(Accelerate | Pheno) & (\"bloodstream infections\" | bacteremia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nNative T1 in acute vs chronic myocarditis", "completion": "(native | T1) & mapping & (acute | chronic) & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate index in sepsis severity", "completion": "(lactate | index) & sepsis & severity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics modeling for CTS care pathways", "completion": "\"system dynamics\" & modeling & (CTS | \"carpal tunnel syndrome\") & (care | pathways)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP thresholds in ICH evacuation", "completion": "(systolic | BP) & thresholds & (ICH | \"intracerebral hemorrhage\") & evacuation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSemi-supervised learning for COVID lymphocyte", "completion": "\"semi supervised learning\" & (COVID | SARS-CoV-2) & lymphocyte"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine dose optimization for toxic nodular goiter", "completion": "(radioiodine | I-131) & dose & optimization & (\"toxic nodular goiter\" | MNG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVicinium for BCG-unresponsive CIS", "completion": "(vicinium | oportuzumab) & (BCG-unresponsive | resistant) & (CIS | \"carcinoma in situ\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after complex PCI", "completion": "(ticagrelor | Brilinta) & monotherapy & (complex | high-risk) & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyroSeq v3 for Bethesda III nodules", "completion": "(ThyroSeq | v3) & (\"Bethesda III\" | indeterminate) & nodules"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in panniculectomy", "completion": "(liposomal | Exparel) & bupivacaine & panniculectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRemote ischemic conditioning for CIN prevention", "completion": "(remote | ischemic) & conditioning & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIdarucizumab for dabigatran reversal", "completion": "(idarucizumab | Praxbind) & (dabigatran | Pradaxa) & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTransformer models for capsule endoscopy video analysis", "completion": "(transformer | models) & (capsule | VCE) & endoscopy & video & analysis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS and emergence delirium", "completion": "(BIS | \"bispectral index\") & (emergence | delirium)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and ICU-acquired weakness", "completion": "(corticosteroids | steroids) & (\"ICU acquired weakness\" | myopathy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntelligent cuff pressure management", "completion": "(intelligent | AI) & cuff & pressure & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT for recurrent CDI in immunocompromised", "completion": "(FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (CDI | \"C. diff infection\") & (immunocompromised | immunosuppressed)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic suturing for GI defects", "completion": "endoscopic & suturing & (\"GI defects\" | perforation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGeneva score revised for PE", "completion": "(Geneva | score) & revised & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in neurogenic shock", "completion": "(angiotensin II | Giapreza) & (neurogenic | spinal) & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUrinary [TIMP-2]\u2022[IGFBP7] for AKI subphenotypes", "completion": "(urinary | [TIMP-2]\u2022[IGFBP7]) & (AKI | \"acute kidney injury\") & subphenotypes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in unilateral lung injury", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (\"unilateral lung\" | injury)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in radiation-induced cardiotoxicity", "completion": "(sacubitril/valsartan | Entresto) & (radiation | radiotherapy) & cardiotoxicity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag in HELLP", "completion": "(lusutrombopag | Mulpleta) & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCloud-based INR monitoring", "completion": "(cloud | digital) & (INR | \"international ratio\") & monitoring"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung-to-head ratio in CDH prognosis", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung to head ratio\" | LHR) & prognosis & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer surgery outcomes", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & outcomes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPoint-of-care CRP for IBD monitoring", "completion": "(point-of-care | POC) & (CRP | \"C-reactive protein\") & (IBD | \"inflammatory bowel disease\") & monitoring"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCost-effectiveness of CYP2C19 testing in DAPT", "completion": "(cost-effectiveness | economic) & (CYP2C19 | genotype) & testing & (DAPT | \"dual antiplatelet therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin clearance in ventilator-associated pneumonia", "completion": "(PCT | procalcitonin) & clearance & (\"ventilator associated\" | VAP) & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCombined biomarkers for PTB prediction", "completion": "combined & biomarkers & prediction & (PTB | \"preterm birth\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine vs benzodiazepines for severe AWS", "completion": "ketamine & (benzodiazepines | BZD) & severe & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed models for pericardiocentesis planning", "completion": "(3D | printed) & models & pericardiocentesis & planning"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous conditioned plasma for hand OA", "completion": "(ACP | \"autologous conditioned plasma\") & (hand | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nERCP timing in gallstone pancreatitis with cholangitis", "completion": "(ERCP | \"endoscopic retrograde cholangiopancreatography\") & timing & (gallstone | biliary) & pancreatitis & cholangitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nModified HScore for secondary HLH", "completion": "(modified | adapted) & (HScore | \"HScore\") & (secondary | HLH)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference-based cuff selection", "completion": "(upper arm | circumference) & based & cuff & selection"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCerebroplacental ratio cutoff for IUGR", "completion": "(cerebroplacental | CPR) & ratio & cutoff & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in statin-associated muscle symptoms", "completion": "(PCSK9 | inhibitor) & (statin | HMG-CoA) & (muscle | symptoms)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and cognition in elderly", "completion": "(vitamin D | cholecalciferol) & cognition & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin albumin combination for HRS", "completion": "(terlipressin | vasopressin) & albumin & combination & (HRS | \"hepatorenal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAmplatzer cribriform for multifenestrated ASD", "completion": "(Amplatzer | cribriform) & device & (multifenestrated | ASD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nQIAstat-Dx for sepsis diagnosis", "completion": "(QIAstat | Dx) & sepsis & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV fraction in cardiac sarcoidosis", "completion": "(ECV | \"extracellular volume\") & fraction & (\"cardiac sarcoid\" | sarcoidosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate-to-albumin ratio prognostic value", "completion": "(lactate/albumin | ratio) & (prognostic | value)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCTS treatment pathways using discrete event simulation", "completion": "(CTS | \"carpal tunnel syndrome\") & (treatment | pathways) & (\"discrete event simulation\" | DES)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltra-early intensive BP lowering in ICH", "completion": "(ultra-early | immediate) & intensive & (BP | \"blood pressure\") & lowering & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte analysis", "completion": "\"self supervised learning\" & lymphocyte & analysis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPersonalized radioiodine dosing for Graves' disease", "completion": "personalized & (radioiodine | I-131) & dosing & (\"Graves'' disease\" | hyperthyroidism)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nInstiladrin for BCG-unresponsive NMIBC", "completion": "(Instiladrin | nadofaragene) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after STEMI", "completion": "(ticagrelor | Brilinta) & monotherapy & (STEMI | \"ST elevation MI\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAfirma Xpression Atlas for thyroid nodules", "completion": "(Afirma | Xpression) & Atlas & (\"thyroid nodules\" | nodules)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap reconstruction", "completion": "(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAtorvastatin for contrast nephropathy", "completion": "(atorvastatin | Lipitor) & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for edoxaban reversal", "completion": "(ciraparantag | PER977) & (edoxaban | Savaysa) & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for capsule endoscopy", "completion": "\"few shot learning\" & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring in elderly surgical patients", "completion": "(BIS | \"bispectral index\") & monitoring & elderly & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and hyperglycemia in sepsis", "completion": "(corticosteroids | steroids) & hyperglycemia & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAI-based cuff pressure optimization", "completion": "(AI | \"artificial intelligence\") & based & cuff & pressure & optimization"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT via upper vs lower GI for CDI", "completion": "(FMT | \"fecal microbiota transplantation\") & (upper | lower) & (GI | route) & (CDI | \"C. diff infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for anastomotic leaks", "completion": "endoscopic & vacuum & (\"anastomotic leaks\" | leak)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSimplified PE diagnostic algorithms", "completion": "simplified & (PE | \"pulmonary embolism\") & diagnostic & algorithms"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric septic shock", "completion": "(angiotensin II | Giapreza) & pediatric & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI phenotyping", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenotyping"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for ventilator-induced lung injury prevention", "completion": "(EIT | \"electrical impedance tomography\") & (\"ventilator induced\" | VILI) & prevention"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in immune checkpoint inhibitor myocarditis", "completion": "(sacubitril/valsartan | Entresto) & (\"immune checkpoint\" | ICI) & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRomiplostim vs eltrombopag in HELLP", "completion": "(romiplostim | eltrombopag) & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlockchain for INR data security", "completion": "blockchain & (INR | \"international ratio\") & (\"data security\" | privacy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI total fetal lung volume in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"total fetal lung volume\" | TFLV) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer surgery cost", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & cost"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSalivary CRP for IBD monitoring", "completion": "(salivary | saliva) & (CRP | \"C-reactive protein\") & (IBD | \"inflammatory bowel disease\") & monitoring"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUniversal vs genotype-guided DAPT", "completion": "(universal | genotype) & guided & (DAPT | \"dual antiplatelet therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin slope in COVID ARDS", "completion": "(PCT | procalcitonin) & slope & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntegrated PTB risk assessment", "completion": "integrated & (PTB | \"preterm birth\") & (risk | assessment)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine-propofol combination for AWS", "completion": "(ketamine | propofol) & combination & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D echo fusion imaging for pericardiocentesis", "completion": "(3D | \"three dimensional\") & (echo | echocardiography) & (fusion | imaging) & pericardiocentesis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for knee OA", "completion": "(autologous | protein) & solution & (knee | OA | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSame-session ERCP in gallstone pancreatitis", "completion": "(same-session | immediate) & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in COVID-associated HLH", "completion": "(HScore | \"HScore\") & (COVID | SARS-CoV-2) & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nForearm BP measurement accuracy", "completion": "(forearm | BP) & measurement & accuracy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVenous Doppler in IUGR management", "completion": "(venous | ductus) & Doppler & (IUGR | \"intrauterine growth restriction\") & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in dialysis patients", "completion": "(PCSK9 | inhibitor) & (dialysis | ESRD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and mortality in elderly", "completion": "(vitamin D | cholecalciferol) & mortality & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs placebo for HRS reversal", "completion": "(terlipressin | vasopressin) & placebo & (HRS | \"hepatorenal syndrome\") & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOcclutech Figulla for ASD closure", "completion": "(Occlutech | Figulla) & device & (ASD | \"atrial septal defect\") & closure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBioFire FilmArray for sepsis", "completion": "(BioFire | FilmArray) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV mapping in amyloidosis", "completion": "(ECV | \"extracellular volume\") & mapping & amyloidosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate index in septic shock resuscitation", "completion": "(lactate | index) & (\"septic shock\" | sepsis) & resuscitation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAgent-based modeling for CTS surgical access", "completion": "\"agent based modeling\" & (CTS | \"carpal tunnel syndrome\") & (surgical | access)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIndividualized BP targets in ICH", "completion": "individualized & (BP | \"blood pressure\") & targets & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContrastive learning for lymphocyte classification", "completion": "\"contrastive learning\" & lymphocyte & classification"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFixed vs calculated radioiodine dose for Graves'", "completion": "(fixed | calculated) & (radioiodine | I-131) & dose & (\"Graves'' disease\" | hyperthyroidism)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAdstiladrin for high-grade Ta NMIBC", "completion": "(Adstiladrin | nadofaragene) & (\"high grade\" | Ta) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after NSTEMI", "completion": "(ticagrelor | Brilinta) & monotherapy & (NSTEMI | \"non ST elevation MI\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyGeNEXT for Hurthle cell lesions", "completion": "(ThyGeNEXT | molecular) & (\"Hurthle cell\" | oncocytic) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TRAM flap", "completion": "(liposomal | Exparel) & bupivacaine & (TRAM | \"breast reconstruction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHigh-dose atorvastatin for CIN prevention", "completion": "(high dose | statin) & atorvastatin & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAndexanet for factor Xa inhibitor reversal", "completion": "(andexanet | Andexxa) & (\"factor Xa\" | inhibitor) & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMeta-learning for capsule endoscopy", "completion": "(meta-learning | transfer) & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in patients with dementia", "completion": "(BIS | \"bispectral index\") & (dementia | cognitive)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone and neuromuscular blocking agents", "completion": "(hydrocortisone | corticosteroid) & (\"neuromuscular blocking agents\" | NMBAs)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPredictive cuff pressure algorithms", "completion": "predictive & cuff & pressure & algorithms"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT donor selection for CDI", "completion": "(FMT | \"fecal microbiota transplantation\") & (donor | selection) & (CDI | \"C. diff infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic suturing vs clips for GI defects", "completion": "endoscopic & (suturing | clips) & (\"GI defects\" | perforation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAge-adjusted D-dimer cutoffs", "completion": "(age-adjusted | modified) & (D-dimer | \"fibrin fragment\") & cutoffs"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric cardiac surgery", "completion": "(angiotensin II | Giapreza) & pediatric & (\"cardiac surgery\" | congenital)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI subtyping", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subtyping"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for prone positioning in ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (prone | positioning) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in Chagas cardiomyopathy", "completion": "(sacubitril/valsartan | Entresto) & (Chagas | cardiomyopathy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag vs romiplostim in HELLP", "completion": "(avatrombopag | romiplostim) & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIoT-based INR monitoring", "completion": "(IoT | \"internet of things\") & (INR | \"international ratio\") & monitoring"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI liver-to-thoracic ratio in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"liver to thoracic ratio\" | LTR) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic vs laparoscopic rectal cancer surgery", "completion": "(robotic | laparoscopic) & (\"rectal cancer\" | rectum) & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nStool calprotectin vs CRP in IBD", "completion": "(stool | fecal) & calprotectin & (CRP | \"C-reactive protein\") & (IBD | \"inflammatory bowel disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCost-effectiveness of genotype-guided DAPT", "completion": "(cost-effectiveness | economic) & (genotype | CYP2C19) & guided & (DAPT | \"dual antiplatelet therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin half-life in septic shock", "completion": "(PCT | procalcitonin) & (\"half life\" | kinetics) & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-modal PTB prediction", "completion": "(multi-modal | combined) & (PTB | \"preterm birth\") & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine adjunct for benzodiazepine-resistant AWS", "completion": "ketamine & adjunct & (benzodiazepine-resistant | refractory) & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed guides for pericardiocentesis", "completion": "(3D | printed) & guides & pericardiocentesis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for knee OA", "completion": "(autologous | micro-fragmented) & (adipose | fat) & tissue & (knee | OA | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nERCP vs surgery for gallstone pancreatitis with choledocholithiasis", "completion": "(ERCP | \"endoscopic retrograde cholangiopancreatography\") & surgery & (gallstone | biliary) & pancreatitis & choledocholithiasis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in lymphoma-associated HLH", "completion": "(HScore | \"HScore\") & (lymphoma | associated) & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWrist BP measurement validation", "completion": "(wrist | BP) & measurement & validation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDuctus venosus a-wave in IUGR", "completion": "(ductus venosus | DV) & (a-wave | reversal) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in chronic kidney disease", "completion": "(PCSK9 | inhibitor) & (CKD | \"chronic kidney disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and infection risk in elderly", "completion": "(vitamin D | cholecalciferol) & (infection | risk) & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin albumin vs albumin alone for HRS", "completion": "(terlipressin | vasopressin) & albumin & (albumin | alone) & (HRS | \"hepatorenal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGore Cardioform ASD occluder", "completion": "(Gore | Cardioform) & device & (ASD | \"atrial septal defect\") & occluder"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSeptiFast for neonatal sepsis", "completion": "(SeptiFast | PCR) & neonatal & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in Anderson-Fabry disease", "completion": "(ECV | \"extracellular volume\") & (\"Anderson-Fabry\" | Fabry)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio in sepsis stratification", "completion": "(lactate/albumin | ratio) & sepsis & stratification"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS surgical wait times", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"wait times\" | access)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIndividualized systolic BP in ICH", "completion": "individualized & (systolic | BP) & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised contrastive learning for lymphocytes", "completion": "\"self supervised\" & \"contrastive learning\" & lymphocytes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMIRD vs fixed dose for Graves' radioiodine", "completion": "(MIRD | \"medical internal radiation dose\") & (fixed | dose) & (\"Graves'' disease\" | radioiodine)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCG0070 for BCG-unresponsive bladder cancer", "completion": "(CG0070 | oncolytic) & (BCG-unresponsive | resistant) & (\"bladder cancer\" | NMIBC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after CABG", "completion": "(ticagrelor | Brilinta) & monotherapy & (CABG | \"coronary artery bypass\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyraMIR for indeterminate thyroid cytology", "completion": "(ThyraMIR | miRNA) & (indeterminate | Bethesda) & thyroid & cytology"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap donor site", "completion": "(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & (\"donor site\" | abdomen)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin for CIN prevention", "completion": "(rosuvastatin | Crestor) & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for apixaban reversal", "completion": "(ciraparantag | PER977) & (apixaban | Eliquis) & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTransformer networks for capsule endoscopy", "completion": "(transformer | networks) & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring in obstructive sleep apnea", "completion": "(BIS | \"bispectral index\") & monitoring & (OSA | \"obstructive sleep apnea\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and secondary infections in sepsis", "completion": "(corticosteroids | steroids) & (\"secondary infections\" | infection) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMachine learning for cuff pressure prediction", "completion": "\"machine learning\" & cuff & pressure & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUniversal donor FMT for CDI", "completion": "(universal | donor) & (FMT | \"fecal microbiota transplantation\") & (CDI | \"C. diff infection\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for esophageal leaks", "completion": "endoscopic & vacuum & (\"esophageal leaks\" | leak)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for clinical probability", "completion": "(D-dimer | \"fibrin fragment\") & adjusted & (\"clinical probability\" | Wells)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in neonates with septic shock", "completion": "(angiotensin II | Giapreza) & neonates & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI treatment response", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & (treatment | response)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT-guided PEEP in COVID ARDS", "completion": "(EIT | \"electrical impedance tomography\") & guided & (PEEP | \"positive end-expiratory pressure\") & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in peripartum cardiomyopathy", "completion": "(sacubitril/valsartan | Entresto) & (\"peripartum cardiomyopathy\" | PPCM)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag vs avatrombopag in HELLP", "completion": "(lusutrombopag | avatrombopag) & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSmartphone-based INR monitoring", "completion": "(smartphone | mobile) & (INR | \"international ratio\") & monitoring"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI observed/expected TFLV in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"observed/expected total fetal lung volume\" | O/E TFLV) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCost analysis robotic vs laparoscopic rectal surgery", "completion": "cost & analysis & robotic & laparoscopic & (rectal | surgery)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin thresholds in UC remission", "completion": "(fecal | stool) & calprotectin & thresholds & (UC | \"ulcerative colitis\") & remission"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nReal-world cost-effectiveness of genotype-guided DAPT", "completion": "(real-world | effectiveness) & (genotype | CYP2C19) & guided & (DAPT | \"dual antiplatelet therapy\") & (cost | economic)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics as antibiotic stewardship tool", "completion": "(PCT | procalcitonin) & kinetics & (antibiotic | stewardship) & tool"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omic PTB prediction", "completion": "(multi-omic | integrated) & (PTB | \"preterm birth\") & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine infusion protocols for AWS", "completion": "ketamine & infusion & protocols & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPatient-specific 3D models for pericardiocentesis", "completion": "(patient-specific | 3D) & models & pericardiocentesis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for hand OA", "completion": "(autologous | protein) & solution & (hand | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEarly ERCP in predicted severe gallstone pancreatitis", "completion": "early & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & predicted & severe & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nModified HScore for COVID-associated HLH", "completion": "(modified | adapted) & (HScore | \"HScore\") & (COVID | SARS-CoV-2) & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference and cuff size", "completion": "(upper arm | circumference) & cuff & size"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUmbilical vein Doppler in IUGR", "completion": "(umbilical | UV) & vein & Doppler & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in heart transplant recipients", "completion": "(PCSK9 | inhibitor) & (\"heart transplant\" | transplantation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and COVID outcomes in elderly", "completion": "(vitamin D | cholecalciferol) & (COVID | SARS-CoV-2) & outcomes & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs noradrenaline for HRS", "completion": "(terlipressin | vasopressin) & (noradrenaline | norepinephrine) & (HRS | \"hepatorenal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAmplatzer Septal Occluder for secundum ASD", "completion": "(Amplatzer | occluder) & device & (\"secundum ASD\" | ASD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nT2Candida for candidemia", "completion": "(T2Candida | panel) & candidemia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac amyloidosis subtypes", "completion": "(ECV | \"extracellular volume\") & (\"cardiac amyloidosis\" | AL | ATTR)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance rate in septic shock", "completion": "(lactate | clearance) & rate & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS post-operative care", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"post operative\" | recovery)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP variability and ICH outcome", "completion": "(systolic | BP) & variability & (ICH | \"intracerebral hemorrhage\") & outcome"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for COVID lymphocytes", "completion": "\"self supervised learning\" & (COVID | SARS-CoV-2) & lymphocytes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMIRD vs standard dose for toxic nodular goiter", "completion": "(MIRD | \"medical internal radiation dose\") & (standard | dose) & (\"toxic nodular goiter\" | MNG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVicineum for NMIBC", "completion": "(vicineum | oportuzumab) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after coronary bypass", "completion": "(ticagrelor | Brilinta) & monotherapy & (\"coronary bypass\" | CABG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAfirma GSC for Hurthle cell nodules", "completion": "(Afirma | GSC) & (\"Hurthle cell\" | oncocytic) & nodules"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in transverse rectus abdominis flap", "completion": "(liposomal | Exparel) & bupivacaine & (TRAM | \"transverse rectus abdominis\") & flap"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHigh-dose rosuvastatin for CIN prevention", "completion": "(high dose | statin) & rosuvastatin & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAndexanet for rivaroxaban reversal", "completion": "(andexanet | Andexxa) & (rivaroxaban | Xarelto) & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVision transformers for capsule endoscopy", "completion": "(ViT | \"vision transformers\") & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in patients with Alzheimer's", "completion": "(BIS | \"bispectral index\") & (\"Alzheimer''s disease\" | dementia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone and insulin resistance in sepsis", "completion": "(hydrocortisone | corticosteroid) & (\"insulin resistance\" | hyperglycemia) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nReinforcement learning for cuff pressure control", "completion": "\"reinforcement learning\" & cuff & pressure & control"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFrozen vs fresh FMT efficacy", "completion": "(frozen | fresh) & (FMT | \"fecal microbiota transplantation\") & efficacy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOverStitch for GI fistula closure", "completion": "(OverStitch | endoscopic) & (fistula | closure)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWells score vs YEARS for PE", "completion": "(Wells | YEARS) & score & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric burn shock", "completion": "(angiotensin II | Giapreza) & pediatric & (\"burn shock\" | burns)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI subphenotyping", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotyping"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP optimization in severe ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & optimization & severe & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in chemotherapy-induced cardiomyopathy", "completion": "(sacubitril/valsartan | Entresto) & (chemotherapy | induced) & cardiomyopathy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag dosing in HELLP", "completion": "(avatrombopag | Doptelet) & dosing & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBlockchain-secured INR data", "completion": "blockchain & secured & (INR | \"international ratio\") & data"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI quantitative lung volume in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (quantitative | volume) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal surgery learning curve", "completion": "robotic & (rectal | surgery) & (\"learning curve\" | training)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC endoscopic remission", "completion": "(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (endoscopic | remission)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCost-utility of genotype-guided DAPT", "completion": "(cost-utility | economic) & (genotype | CYP2C19) & guided & (DAPT | \"dual antiplatelet therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin slope as predictor of VAP", "completion": "(PCT | procalcitonin) & slope & predictor & (VAP | \"ventilator associated pneumonia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIntegrated multi-omics for PTB", "completion": "integrated & (multi-omics | biomarkers) & (PTB | \"preterm birth\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine-benzodiazepine combination for AWS", "completion": "(ketamine | benzodiazepine) & combination & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D echo navigation for pericardiocentesis", "completion": "(3D | \"three dimensional\") & (echo | echocardiography) & navigation & pericardiocentesis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for hand OA", "completion": "(autologous | micro-fragmented) & (adipose | fat) & tissue & (hand | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy after mild gallstone pancreatitis", "completion": "(same-admission | immediate) & cholecystectomy & mild & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in adult-onset Still's disease", "completion": "(HScore | \"HScore\") & (\"adult onset Still''s disease\" | AOSD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nForearm BP in obesity", "completion": "(forearm | BP) & measurement & obesity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUmbilical artery Doppler pulsatility index in IUGR", "completion": "(umbilical | UA) & Doppler & (PI | \"pulsatility index\") & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in familial hypercholesterolemia children", "completion": "(PCSK9 | inhibitor) & (FH | \"familial hypercholesterolemia\") & children"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and pneumonia in elderly", "completion": "(vitamin D | cholecalciferol) & pneumonia & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs octreotide for variceal bleeding", "completion": "(terlipressin | vasopressin) & (octreotide | Sandostatin) & (\"variceal bleeding\" | hemorrhage)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGore device for PFO closure", "completion": "(Gore | device) & (PFO | \"patent foramen ovale\") & closure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBioFire Blood Culture Identification 2", "completion": "(BioFire | BCID2) & (\"blood culture\" | identification)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in hypertrophic cardiomyopathy", "completion": "(ECV | \"extracellular volume\") & (HCM | \"hypertrophic cardiomyopathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio in sepsis mortality prediction", "completion": "(lactate/albumin | ratio) & sepsis & mortality & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS rehabilitation pathways", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (rehabilitation | pathways)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTime-dependent BP control in ICH", "completion": "(time-dependent | temporal) & (BP | \"blood pressure\") & control & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContrastive self-supervised learning for lymphocytes", "completion": "\"contrastive self supervised learning\" & lymphocytes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCalculated radioiodine dose for toxic MNG", "completion": "(calculated | MIRD) & (radioiodine | I-131) & dose & (\"toxic MNG\" | multinodular)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nInstiladrin for CIS", "completion": "(Instiladrin | nadofaragene) & (CIS | \"carcinoma in situ\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after acute MI", "completion": "(ticagrelor | Brilinta) & monotherapy & (\"acute MI\" | AMI)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyGeNEXT for follicular lesions", "completion": "(ThyGeNEXT | molecular) & (follicular | lesions) & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in abdominal surgery", "completion": "(liposomal | Exparel) & bupivacaine & abdominal & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nStatin loading for CIN prevention", "completion": "(statin | loading) & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIdarucizumab for dabigatran reversal in ICH", "completion": "(idarucizumab | Praxbind) & (dabigatran | Pradaxa) & reversal & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMeta-learning for capsule endoscopy few-shot", "completion": "(meta-learning | few-shot) & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring in Down syndrome", "completion": "(BIS | \"bispectral index\") & monitoring & (\"Down syndrome\" | trisomy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and glycemic variability in sepsis", "completion": "(corticosteroids | steroids) & (glycemic | glucose) & variability & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDeep reinforcement learning for cuff pressure", "completion": "\"deep reinforcement learning\" & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT microbiota engraftment", "completion": "(FMT | \"fecal microbiota transplantation\") & (microbiota | engraftment)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for duodenal leaks", "completion": "endoscopic & vacuum & (duodenal | leak)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSimplified YEARS algorithm", "completion": "simplified & (YEARS | algorithm) & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric septic shock dose", "completion": "(angiotensin II | Giapreza) & pediatric & (\"septic shock\" | sepsis) & dose"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for persistent AKI", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (persistent | AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in pediatric ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & pediatric & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in diabetic cardiomyopathy", "completion": "(sacubitril/valsartan | Entresto) & (diabetic | diabetes) & cardiomyopathy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag dosing in HELLP", "completion": "(lusutrombopag | Mulpleta) & dosing & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSecure multiparty computation for INR data", "completion": "(secure | multiparty) & computation & (INR | \"international ratio\") & data"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume percentiles in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume\" | percentiles) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal surgery outcomes", "completion": "robotic & (rectal | surgery) & outcomes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for predicting UC relapse", "completion": "(fecal | stool) & calprotectin & predicting & (UC | \"ulcerative colitis\") & relapse"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBudget impact of genotype-guided DAPT", "completion": "(budget | impact) & (genotype | CYP2C19) & guided & (DAPT | \"dual antiplatelet therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin velocity in ventilator-associated pneumonia", "completion": "(PCT | procalcitonin) & velocity & (\"ventilator associated\" | VAP) & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-modal PTB risk model", "completion": "(multi-modal | integrated) & (PTB | \"preterm birth\") & (risk | model)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS in cirrhosis", "completion": "ketamine & (AWS | \"alcohol withdrawal syndrome\") & cirrhosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed personalized pericardiocentesis guides", "completion": "(3D | printed) & personalized & pericardiocentesis & guides"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for thumb CMC OA", "completion": "(autologous | protein) & solution & (\"thumb CMC\" | basal) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIndex cholecystectomy timing after gallstone pancreatitis", "completion": "(index | interval) & cholecystectomy & timing & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in systemic juvenile idiopathic arthritis", "completion": "(HScore | \"HScore\") & (sJIA | \"systemic juvenile idiopathic arthritis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWrist BP measurement in large arms", "completion": "(wrist | BP) & measurement & (large | arms)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCerebral-placental ratio in IUGR prediction", "completion": "(cerebral-placental | CPR) & ratio & (IUGR | \"intrauterine growth restriction\") & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in nephrotic syndrome", "completion": "(PCSK9 | inhibitor) & (\"nephrotic syndrome\" | proteinuria)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ARDS outcomes", "completion": "(vitamin D | cholecalciferol) & (ARDS | \"acute respiratory distress syndrome\") & outcomes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for acute variceal bleeding", "completion": "(terlipressin | vasopressin) & acute & (\"variceal bleeding\" | hemorrhage)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOcclutech ASD device", "completion": "(Occlutech | device) & (ASD | \"atrial septal defect\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSeptiFast for pediatric sepsis", "completion": "(SeptiFast | PCR) & pediatric & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in iron overload cardiomyopathy", "completion": "(ECV | \"extracellular volume\") & (\"iron overload\" | hemochromatosis) & cardiomyopathy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio vs SOFA in sepsis", "completion": "(lactate/albumin | ratio) & (SOFA | \"sequential organ failure assessment\") & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS economic burden", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (economic | burden)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP trajectories in ICH recovery", "completion": "(systolic | BP) & trajectories & (ICH | \"intracerebral hemorrhage\") & recovery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for COVID lymphocyte morphology", "completion": "\"self supervised learning\" & (COVID | SARS-CoV-2) & lymphocyte & morphology"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMIRD dose calculation for toxic nodular goiter", "completion": "(MIRD | \"medical internal radiation dose\") & dose & calculation & (\"toxic nodular goiter\" | MNG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAdstiladrin for BCG-unresponsive NMIBC", "completion": "(Adstiladrin | nadofaragene) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after complex PCI", "completion": "(ticagrelor | Brilinta) & monotherapy & (complex | high-risk) & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyraMIR for indeterminate FNA", "completion": "(ThyraMIR | miRNA) & (indeterminate | FNA | \"fine needle aspiration\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap pain", "completion": "(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & pain"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin loading for CIN prevention", "completion": "(rosuvastatin | loading) & prevention & (CIN | \"contrast-induced nephropathy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for betrixaban reversal", "completion": "(ciraparantag | PER977) & (betrixaban | Bevyxxa) & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVision transformers for small bowel capsule endoscopy", "completion": "(ViT | \"vision transformers\") & (\"small bowel\" | SB) & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in Parkinson's disease", "completion": "(BIS | \"bispectral index\") & (\"Parkinson''s disease\" | PD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and mitochondrial function in sepsis", "completion": "(corticosteroids | steroids) & (mitochondrial | function) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFederated learning for cuff pressure prediction", "completion": "\"federated learning\" & cuff & pressure & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDonor screening for FMT", "completion": "(donor | screening) & (FMT | \"fecal microbiota transplantation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for pancreatic leaks", "completion": "endoscopic & vacuum & (pancreatic | leak)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for age and comorbidity", "completion": "(D-dimer | \"fibrin fragment\") & adjusted & (age | comorbidity)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in extracorporeal membrane oxygenation", "completion": "(angiotensin II | Giapreza) & (ECMO | \"extracorporeal membrane oxygenation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI phenogroups", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenogroups"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP individualization in ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & individualization & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in iron deficiency heart failure", "completion": "(sacubitril/valsartan | Entresto) & (\"iron deficiency\" | ID) & (\"heart failure\" | HF)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag in HELLP with thrombocytopenia", "completion": "(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & thrombocytopenia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHomomorphic encryption for INR data", "completion": "(homomorphic | encryption) & (INR | \"international ratio\") & data"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI observed/expected lung volume in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"observed/expected lung volume\" | O/E LV) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer functional outcomes", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & (functional | outcomes)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC treatment de-escalation", "completion": "(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (\"de escalation\" | reduction)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nReal-world CYP2C19 testing in DAPT", "completion": "(real-world | effectiveness) & (CYP2C19 | genotype) & testing & (DAPT | \"dual antiplatelet therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for antibiotic duration", "completion": "(PCT | procalcitonin) & kinetics & antibiotics & duration"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omic integration for PTB", "completion": "(multi-omic | integration) & (PTB | \"preterm birth\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine protocols for AWS in ICU", "completion": "ketamine & protocols & (AWS | \"alcohol withdrawal syndrome\") & (ICU | \"intensive care\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D echo-CT fusion for complex effusions", "completion": "(3D | \"three dimensional\") & (echo | CT) & fusion & (complex | effusions)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for first CMC OA", "completion": "(autologous | protein) & solution & (\"first CMC\" | thumb) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSame-session ERCP cholecystectomy", "completion": "(same-session | combined) & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & cholecystectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in macrophage activation syndrome", "completion": "(HScore | \"HScore\") & (MAS | \"macrophage activation syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference-based cuff size", "completion": "(upper arm | circumference) & based & cuff & size"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMiddle cerebral artery PI in IUGR", "completion": "(MCA | \"middle cerebral artery\") & (PI | \"pulsatility index\") & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in liver transplant recipients", "completion": "(PCSK9 | inhibitor) & (\"liver transplant\" | transplantation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and sepsis mortality", "completion": "(vitamin D | cholecalciferol) & sepsis & mortality"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs vasopressin for varices", "completion": "(terlipressin | vasopressin) & (varices | variceal)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAmplatzer PFO occluder", "completion": "(Amplatzer | occluder) & (PFO | \"patent foramen ovale\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nT2Bacteria for rapid diagnosis", "completion": "(T2Bacteria | panel) & rapid & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac involvement of systemic diseases", "completion": "(ECV | \"extracellular volume\") & (cardiac | heart) & (systemic | diseases)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time prognostic value", "completion": "(lactate | clearance) & time & (prognostic | value)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS return-to-work", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"return to work\" | recovery)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and hematoma growth in ICH", "completion": "(systolic | BP) & (\"hematoma growth\" | expansion) & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte subpopulations", "completion": "\"self supervised learning\" & lymphocyte & subpopulations"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine dose calculation methods", "completion": "(radioiodine | I-131) & dose & calculation & methods"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCG0070 for high-risk NMIBC", "completion": "(CG0070 | oncolytic) & (\"high risk\" | high-risk) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after drug-eluting stent", "completion": "(ticagrelor | Brilinta) & monotherapy & (DES | \"drug-eluting stent\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAfirma Xpression for thyroid cancer", "completion": "(Afirma | Xpression) & (\"thyroid cancer\" | malignancy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in transverse abdominis plane block", "completion": "(liposomal | Exparel) & bupivacaine & (TAP | \"transverse abdominis plane\") & block"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAtorvastatin for CIN prevention in CKD", "completion": "(atorvastatin | Lipitor) & prevention & (CIN | \"contrast-induced nephropathy\") & (CKD | \"chronic kidney disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAndexanet for apixaban reversal", "completion": "(andexanet | Andexxa) & (apixaban | Eliquis) & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVision transformers for ulcer detection", "completion": "(ViT | \"vision transformers\") & ulcer & detection"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in multiple sclerosis", "completion": "(BIS | \"bispectral index\") & (MS | \"multiple sclerosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and immune paralysis in sepsis", "completion": "(corticosteroids | steroids) & (\"immune paralysis\" | immunosuppression) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nReinforcement learning for cuff pressure optimization", "completion": "\"reinforcement learning\" & cuff & pressure & optimization"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT donor-recipient matching", "completion": "(FMT | \"fecal microbiota transplantation\") & (donor | recipient) & matching"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for colorectal leaks", "completion": "endoscopic & vacuum & (colorectal | leak)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWells score in elderly for PE", "completion": "(Wells | score) & elderly & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in congenital heart disease surgery", "completion": "(angiotensin II | Giapreza) & (\"congenital heart disease\" | CHD) & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI subphenotypes in sepsis", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotypes & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for personalized ventilation in ARDS", "completion": "(EIT | \"electrical impedance tomography\") & personalized & ventilation & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in tachycardia-induced cardiomyopathy", "completion": "(sacubitril/valsartan | Entresto) & (\"tachycardia induced\" | cardiomyopathy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag vs placebo in HELLP", "completion": "(avatrombopag | Doptelet) & placebo & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nZero-knowledge proofs for INR privacy", "completion": "(zero-knowledge | proofs) & (INR | \"international ratio\") & privacy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung area quantification in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung area\" | quantification) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer quality of life", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & (QoL | \"quality of life\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD postoperative recurrence", "completion": "(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (postoperative | recurrence)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing implementation challenges", "completion": "(CYP2C19 | genotype) & testing & (implementation | challenges)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin half-life as antibiotic guide", "completion": "(PCT | procalcitonin) & (\"half life\" | kinetics) & (antibiotic | guide)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction model", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & (prediction | model)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS in liver disease", "completion": "ketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"liver disease\" | cirrhosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed patient-specific pericardiocentesis models", "completion": "(3D | printed) & patient-specific & pericardiocentesis & models"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for thumb OA", "completion": "(autologous | micro-fragmented) & (adipose | fat) & tissue & (thumb | CMC) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy after endoscopic stone removal", "completion": "(same-admission | immediate) & cholecystectomy & endoscopic & (stone | removal)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in secondary HLH validation", "completion": "(HScore | \"HScore\") & (secondary | HLH) & validation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nForearm BP measurement protocols", "completion": "(forearm | BP) & measurement & protocols"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUmbilical artery resistance index in IUGR", "completion": "(umbilical | UA) & (RI | \"resistance index\") & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in HIV-associated dyslipidemia", "completion": "(PCSK9 | inhibitor) & (HIV | \"human immunodeficiency virus\") & dyslipidemia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ventilator weaning", "completion": "(vitamin D | cholecalciferol) & (ventilator | weaning)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin in acute kidney injury with cirrhosis", "completion": "(terlipressin | vasopressin) & (AKI | \"acute kidney injury\") & cirrhosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGore Cardioform for secundum ASD", "completion": "(Gore | Cardioform) & device & (\"secundum ASD\" | ASD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBioFire for pediatric bloodstream infections", "completion": "(BioFire | FilmArray) & pediatric & (\"bloodstream infections\" | bacteremia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in chemotherapy cardiotoxicity", "completion": "(ECV | \"extracellular volume\") & (chemotherapy | cardiotoxicity)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio for sepsis severity", "completion": "(lactate/albumin | ratio) & sepsis & severity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS cost-effectiveness", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (cost-effectiveness | economic)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUltra-early systolic BP reduction in ICH", "completion": "(ultra-early | immediate) & (systolic | BP) & reduction & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nContrastive self-supervised learning for lymphocyte subtypes", "completion": "\"contrastive self supervised learning\" & lymphocyte & subtypes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nActivity-based radioiodine dosing for Graves' disease", "completion": "(activity | MIRD) & based & (radioiodine | I-131) & dosing & (\"Graves'' disease\" | hyperthyroidism)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nInstiladrin for BCG-unresponsive CIS", "completion": "(Instiladrin | nadofaragene) & (BCG-unresponsive | resistant) & (CIS | \"carcinoma in situ\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after myocardial infarction", "completion": "(ticagrelor | Brilinta) & monotherapy & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRosettaGX Reveal for Hurthle cell lesions", "completion": "(RosettaGX | Reveal) & (\"Hurthle cell\" | oncocytic) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in breast surgery", "completion": "(liposomal | Exparel) & bupivacaine & breast & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHigh-dose atorvastatin for CIN in diabetes", "completion": "(high dose | statin) & atorvastatin & (CIN | \"contrast-induced nephropathy\") & diabetes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIdarucizumab for ICH on dabigatran", "completion": "(idarucizumab | Praxbind) & (ICH | \"intracerebral hemorrhage\") & (dabigatran | Pradaxa)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for capsule endoscopy lesions", "completion": "\"few shot learning\" & (capsule | VCE) & endoscopy & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in epilepsy surgery", "completion": "(BIS | \"bispectral index\") & (epilepsy | seizures) & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and microbiome in sepsis", "completion": "(corticosteroids | steroids) & (microbiome | microbiota) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDeep RL for cuff pressure regulation", "completion": "\"deep reinforcement learning\" & cuff & pressure & regulation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT engraftment biomarkers", "completion": "(FMT | \"fecal microbiota transplantation\") & engraftment & biomarkers"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-surgical leaks", "completion": "endoscopic & vacuum & (\"post surgical\" | post-operative) & leaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSimplified Wells score for PE", "completion": "simplified & (Wells | score) & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in neonates on ECMO", "completion": "(angiotensin II | Giapreza) & neonates & (ECMO | \"extracorporeal membrane oxygenation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI phenotyping in COVID", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenotyping & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in obesity ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & obesity & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in Takotsubo cardiomyopathy", "completion": "(sacubitril/valsartan | Entresto) & (Takotsubo | cardiomyopathy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag in HELLP syndrome", "completion": "(lusutrombopag | Mulpleta) & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSecure enclaves for INR data", "completion": "(secure | enclaves) & (INR | \"international ratio\") & data"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume Z-scores in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume\" | Z-scores) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer sexual function", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & (\"sexual function\" | outcomes)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for predicting CD surgery", "completion": "(fecal | stool) & calprotectin & predicting & (CD | \"Crohn''s disease\") & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in clinical practice", "completion": "(CYP2C19 | genotype) & testing & (\"clinical practice\" | implementation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for VAP diagnosis", "completion": "(PCT | procalcitonin) & kinetics & (VAP | \"ventilator associated pneumonia\") & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB risk stratification", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & (risk | stratification)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine for refractory AWS in ICU", "completion": "ketamine & (refractory | resistant) & (AWS | \"alcohol withdrawal syndrome\") & (ICU | \"intensive care\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed models for complex pericardiocentesis", "completion": "(3D | printed) & models & complex & pericardiocentesis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for erosive hand OA", "completion": "(autologous | protein) & solution & (erosive | hand) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEarly cholecystectomy after endoscopic sphincterotomy", "completion": "early & cholecystectomy & (\"endoscopic sphincterotomy\" | ERCP)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in COVID cytokine storm", "completion": "(HScore | \"HScore\") & (COVID | SARS-CoV-2) & (\"cytokine storm\" | hyperinflammation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThigh cuff BP measurement", "completion": "(thigh | cuff) & (BP | \"blood pressure\") & measurement"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCerebroplacental ratio in late IUGR management", "completion": "(cerebroplacental | CPR) & ratio & (late | onset) & (IUGR | \"intrauterine growth restriction\") & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in post-kidney transplant", "completion": "(PCSK9 | inhibitor) & (\"kidney transplant\" | transplantation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ICU outcomes", "completion": "(vitamin D | cholecalciferol) & (ICU | \"intensive care\") & outcomes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for type 1 HRS", "completion": "(terlipressin | vasopressin) & (\"type 1\" | HRS) & (\"hepatorenal syndrome\" | HRS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOcclutech PFO device", "completion": "(Occlutech | device) & (PFO | \"patent foramen ovale\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAccelerate Pheno for pediatric bacteremia", "completion": "(Accelerate | Pheno) & pediatric & bacteremia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac amyloid vs hypertrophic cardiomyopathy", "completion": "(ECV | \"extracellular volume\") & (amyloid | HCM) & cardiomyopathy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance rate prognostic accuracy", "completion": "(lactate | clearance) & rate & (prognostic | accuracy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS disability prevention", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (disability | prevention)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and functional outcome in ICH", "completion": "(systolic | BP) & (functional | outcome) & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte dynamics", "completion": "\"self supervised learning\" & lymphocyte & dynamics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMIRD vs fixed dose for toxic adenoma", "completion": "(MIRD | \"medical internal radiation dose\") & (fixed | dose) & (\"toxic adenoma\" | nodule)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCG0070 for BCG-unresponsive bladder CIS", "completion": "(CG0070 | oncolytic) & (BCG-unresponsive | resistant) & (\"bladder CIS\" | carcinoma)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after stent thrombosis", "completion": "(ticagrelor | Brilinta) & monotherapy & (\"stent thrombosis\" | thrombosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyraMIR for indeterminate thyroid lesions", "completion": "(ThyraMIR | miRNA) & (indeterminate | Bethesda) & thyroid & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap reconstruction pain", "completion": "(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & pain"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin for CIN in renal impairment", "completion": "(rosuvastatin | Crestor) & (CIN | \"contrast-induced nephropathy\") & (\"renal impairment\" | CKD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for edoxaban reversal", "completion": "(ciraparantag | PER977) & (edoxaban | Savaysa) & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVision transformers for bleeding detection in capsule endoscopy", "completion": "(ViT | \"vision transformers\") & (bleeding | hemorrhage) & detection & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in traumatic brain injury", "completion": "(BIS | \"bispectral index\") & (TBI | \"traumatic brain injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and endothelial dysfunction in sepsis", "completion": "(corticosteroids | steroids) & (endothelial | dysfunction) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFederated reinforcement learning for cuff pressure", "completion": "\"federated reinforcement learning\" & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT donor microbiome composition", "completion": "(FMT | \"fecal microbiota transplantation\") & (donor | microbiome) & composition"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for bariatric surgery leaks", "completion": "endoscopic & vacuum & (bariatric | surgery) & leaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for renal function", "completion": "(D-dimer | \"fibrin fragment\") & adjusted & (\"renal function\" | CKD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric cardiac arrest", "completion": "(angiotensin II | Giapreza) & pediatric & (\"cardiac arrest\" | resuscitation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI phenogroups in sepsis", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenogroups & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in unilateral pneumonia", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (unilateral | pneumonia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in perimyocarditis", "completion": "(sacubitril/valsartan | Entresto) & perimyocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag for HELLP thrombocytopenia", "completion": "(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & thrombocytopenia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDifferential privacy for INR data", "completion": "(differential | privacy) & (INR | \"international ratio\") & data"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung-to-thorax ratio in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung to thorax ratio\" | LTR) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer bowel function", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & (\"bowel function\" | outcomes)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC de-escalation criteria", "completion": "(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (\"de escalation\" | criteria)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBarriers to CYP2C19 testing", "completion": "barriers & (CYP2C19 | genotype) & testing"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for antibiotic stewardship", "completion": "(PCT | procalcitonin) & kinetics & (antibiotic | stewardship)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in high-risk women", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (\"high risk\" | high-risk) & women"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine vs benzodiazepines for AWS in ICU", "completion": "ketamine & (benzodiazepines | BZD) & (AWS | \"alcohol withdrawal syndrome\") & (ICU | \"intensive care\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D echo-CT fusion for complex pericardial effusions", "completion": "(3D | \"three dimensional\") & (echo | CT) & fusion & (complex | \"pericardial effusions\" | fluid)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for first carpometacarpal OA", "completion": "(autologous | protein) & solution & (\"first carpometacarpal\" | CMC) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSame-session ERCP and laparoscopic cholecystectomy", "completion": "(same-session | combined) & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & laparoscopic & cholecystectomy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in systemic lupus erythematosus", "completion": "(HScore | \"HScore\") & (SLE | \"systemic lupus erythematosus\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference and BP accuracy", "completion": "(upper arm | circumference) & (BP | \"blood pressure\") & accuracy"}
