{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMiddle cerebral artery Doppler in early IUGR", "completion": "(MCA | \"middle cerebral artery\") & Doppler & (early | onset) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in familial combined hyperlipidemia", "completion": "(PCSK9 | inhibitor) & (\"familial combined hyperlipidemia\" | FCHL)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ARDS prevention", "completion": "(vitamin D | cholecalciferol) & (ARDS | \"acute respiratory distress syndrome\") & prevention"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for hepatorenal syndrome acute kidney injury", "completion": "(terlipressin | vasopressin) & (HRS | \"hepatorenal syndrome\") & (AKI | \"acute kidney injury\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAmplatzer PFO occluder long-term outcomes", "completion": "(Amplatzer | occluder) & (PFO | \"patent foramen ovale\") & \"long term\" & outcomes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nT2Candida for neonatal candidiasis", "completion": "(T2Candida | panel) & neonatal & candidiasis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac sarcoid vs giant cell myocarditis", "completion": "(ECV | \"extracellular volume\") & (\"cardiac sarcoid\" | \"giant cell myocarditis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time in septic shock mortality", "completion": "(lactate | clearance) & time & (\"septic shock\" | sepsis) & mortality"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS prevention programs", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (prevention | programs)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP reduction speed in ICH", "completion": "(systolic | BP) & reduction & speed & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte kinetics", "completion": "\"self supervised learning\" & lymphocyte & kinetics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPersonalized radioiodine for toxic MNG", "completion": "personalized & (radioiodine | I-131) & (\"toxic MNG\" | multinodular)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVicinium for BCG-unresponsive NMIBC", "completion": "(vicinium | oportuzumab) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after left main PCI", "completion": "(ticagrelor | Brilinta) & monotherapy & (\"left main\" | LM) & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAfirma GSC for medullary thyroid cancer", "completion": "(Afirma | GSC) & (\"medullary thyroid cancer\" | MTC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TRAM flap donor site", "completion": "(liposomal | Exparel) & bupivacaine & (TRAM | \"breast reconstruction\") & (\"donor site\" | abdomen)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAtorvastatin for CIN prevention in STEMI", "completion": "(atorvastatin | Lipitor) & prevention & (CIN | \"contrast-induced nephropathy\") & (STEMI | \"ST elevation MI\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAndexanet for factor Xa inhibitor reversal in ICH", "completion": "(andexanet | Andexxa) & (\"factor Xa\" | inhibitor) & reversal & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for rare capsule endoscopy findings", "completion": "\"few shot learning\" & (capsule | VCE) & endoscopy & (rare | findings)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in Huntington's disease", "completion": "(BIS | \"bispectral index\") & (\"Huntington''s disease\" | chorea)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and coagulation in sepsis", "completion": "(corticosteroids | steroids) & coagulation & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMeta-reinforcement learning for cuff pressure", "completion": "\"meta reinforcement learning\" & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT donor microbiome diversity", "completion": "(FMT | \"fecal microbiota transplantation\") & (donor | microbiome) & diversity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for pancreaticoduodenectomy leaks", "completion": "endoscopic & vacuum & (pancreaticoduodenectomy | Whipple) & leaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for D-dimer assay", "completion": "(D-dimer | \"fibrin fragment\") & adjusted & assay"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric septic shock with ARDS", "completion": "(angiotensin II | Giapreza) & pediatric & (\"septic shock\" | sepsis) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI endotypes", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & endotypes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in COVID pneumonia", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (COVID | SARS-CoV-2) & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in Chagas heart disease", "completion": "(sacubitril/valsartan | Entresto) & (Chagas | cardiomyopathy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag for HELLP platelet recovery", "completion": "(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & (platelet | recovery)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPrivacy-preserving INR analytics", "completion": "(privacy | preserving) & (INR | \"international ratio\") & analytics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI total lung volume prediction in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"total lung volume\" | TLV) & prediction & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer urinary function", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & (\"urinary function\" | outcomes)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD de-escalation", "completion": "(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (\"de escalation\" | reduction)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing cost-effectiveness analysis", "completion": "(CYP2C19 | genotype) & testing & (cost-effectiveness | economic) & analysis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin half-life for VAP management", "completion": "(PCT | procalcitonin) & (\"half life\" | kinetics) & (VAP | \"ventilator associated pneumonia\") & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in twins", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & twins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS with hepatic encephalopathy", "completion": "ketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"hepatic encephalopathy\" | cirrhosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed patient-specific pericardiocentesis training", "completion": "(3D | printed) & patient-specific & pericardiocentesis & training"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for base of thumb OA", "completion": "(autologous | micro-fragmented) & (adipose | fat) & tissue & (\"base of thumb\" | CMC) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEarly cholecystectomy after ERCP for choledocholithiasis", "completion": "early & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & choledocholithiasis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in Kawasaki disease", "completion": "(HScore | \"HScore\") & (\"Kawasaki disease\" | vasculitis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nForearm BP in large arms", "completion": "(forearm | BP) & measurement & (large | arms)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUmbilical vein pulsations in IUGR", "completion": "(umbilical | UV) & vein & pulsations & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in metabolic syndrome", "completion": "(PCSK9 | inhibitor) & (\"metabolic syndrome\" | insulin)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ventilator duration", "completion": "(vitamin D | cholecalciferol) & (ventilator | duration)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for acute-on-chronic liver failure", "completion": "(terlipressin | vasopressin) & (\"acute on chronic\" | ACLF) & (\"liver failure\" | cirrhosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGore Cardioform PFO occluder", "completion": "(Gore | Cardioform) & (PFO | \"patent foramen ovale\") & occluder"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBioFire BCID2 for neonatal sepsis", "completion": "(BioFire | BCID2) & neonatal & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac involvement of rheumatoid arthritis", "completion": "(ECV | \"extracellular volume\") & (cardiac | heart) & (RA | \"rheumatoid arthritis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio for mortality prediction", "completion": "(lactate/albumin | ratio) & mortality & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS occupational health", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (occupational | health)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP trajectory in ICH recovery", "completion": "(systolic | BP) & trajectory & (ICH | \"intracerebral hemorrhage\") & recovery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte activation", "completion": "\"self supervised learning\" & lymphocyte & activation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine dose optimization for toxic adenoma", "completion": "(radioiodine | I-131) & dose & optimization & (\"toxic adenoma\" | nodule)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAdstiladrin for high-grade Ta urothelial carcinoma", "completion": "(Adstiladrin | nadofaragene) & (\"high grade\" | Ta) & (\"urothelial carcinoma\" | NMIBC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after ACS", "completion": "(ticagrelor | Brilinta) & monotherapy & (ACS | \"acute coronary syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyGeNEXT for papillary thyroid carcinoma", "completion": "(ThyGeNEXT | molecular) & (\"papillary thyroid\" | PTC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TAP block", "completion": "(liposomal | Exparel) & bupivacaine & (TAP | \"transverse abdominis plane\") & block"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHigh-dose rosuvastatin for CIN in STEMI", "completion": "(high dose | statin) & rosuvastatin & (CIN | \"contrast-induced nephropathy\") & (STEMI | \"ST elevation MI\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for apixaban reversal", "completion": "(ciraparantag | PER977) & (apixaban | Eliquis) & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVision transformers for polyp detection in capsule endoscopy", "completion": "(ViT | \"vision transformers\") & (polyp | detection) & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in schizophrenia", "completion": "(BIS | \"bispectral index\") & schizophrenia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and gut barrier in sepsis", "completion": "(corticosteroids | steroids) & (\"gut barrier\" | permeability) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-agent RL for cuff pressure", "completion": "\"multi agent reinforcement learning\" & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome predictors of response", "completion": "(FMT | \"fecal microbiota transplantation\") & (microbiome | predictors) & response"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for gastrectomy leaks", "completion": "endoscopic & vacuum & (gastrectomy | leak)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWells score in pregnancy for PE", "completion": "(Wells | score) & pregnancy & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric congenital diaphragmatic hernia", "completion": "(angiotensin II | Giapreza) & pediatric & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI endotypes in COVID", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & endotypes & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in obese COVID ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & obese & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute myocarditis", "completion": "(sacubitril/valsartan | Entresto) & acute & myocarditis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag for HELLP thrombocytopenia", "completion": "(lusutrombopag | Mulpleta) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & thrombocytopenia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSecure multi-party computation for INR", "completion": "(secure | multi-party) & computation & (INR | \"international ratio\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI observed/expected lung volume percentiles", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"observed/expected lung volume\" | O/E LV) & percentiles"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer long-term function", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & \"long term\" & function"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC treatment optimization", "completion": "(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (treatment | optimization)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in community hospitals", "completion": "(CYP2C19 | genotype) & testing & (community | hospitals)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for sepsis subphenotyping", "completion": "(PCT | procalcitonin) & kinetics & sepsis & subphenotyping"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in nulliparous women", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & nulliparous"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine protocols for severe AWS", "completion": "ketamine & protocols & severe & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed phantoms for pericardiocentesis training", "completion": "(3D | printed) & phantoms & pericardiocentesis & training"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for erosive hand osteoarthritis", "completion": "(autologous | protein) & solution & erosive & (hand | osteoarthritis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEarly cholecystectomy after ERCP for gallstone pancreatitis", "completion": "early & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & (gallstone | biliary) & pancreatitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in adult-onset Still's disease validation", "completion": "(HScore | \"HScore\") & (\"adult onset Still''s disease\" | AOSD) & validation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWrist BP in clinical practice", "completion": "(wrist | BP) & measurement & (\"clinical practice\" | accuracy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDuctus venosus a-wave reversal in IUGR", "completion": "(ductus venosus | DV) & (\"a-wave reversal\" | reversal) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in homozygous familial hypercholesterolemia", "completion": "(PCSK9 | inhibitor) & (homozygous | HoFH) & (FH | \"familial hypercholesterolemia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and sepsis incidence", "completion": "(vitamin D | cholecalciferol) & sepsis & incidence"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs norepinephrine for septic shock in cirrhosis", "completion": "(terlipressin | vasopressin) & (norepinephrine | noradrenaline) & (\"septic shock\" | sepsis) & cirrhosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOcclutech Figulla Flex II ASD", "completion": "(Occlutech | Figulla) & device & (ASD | \"atrial septal defect\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSeptiFast for sepsis in immunocompromised", "completion": "(SeptiFast | PCR) & sepsis & immunocompromised"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac transthyretin amyloidosis", "completion": "(ECV | \"extracellular volume\") & (cardiac | ATTR) & amyloidosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance rate in septic shock resuscitation", "completion": "(lactate | clearance) & rate & (\"septic shock\" | sepsis) & resuscitation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS public health impact", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"public health\" | impact)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and cognitive outcome in ICH", "completion": "(systolic | BP) & (cognitive | outcome) & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte senescence", "completion": "\"self supervised learning\" & lymphocyte & senescence"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMIRD dose for toxic MNG", "completion": "(MIRD | \"medical internal radiation dose\") & dose & (\"toxic MNG\" | multinodular)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCG0070 for carcinoma in situ", "completion": "(CG0070 | oncolytic) & (\"carcinoma in situ\" | CIS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after stroke", "completion": "(ticagrelor | Brilinta) & monotherapy & stroke"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyraMIR for medullary thyroid cancer", "completion": "(ThyraMIR | miRNA) & (\"medullary thyroid\" | MTC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap pain control", "completion": "(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & (pain | control)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin for CIN prevention in CKD", "completion": "(rosuvastatin | Crestor) & prevention & (CIN | \"contrast-induced nephropathy\") & (CKD | \"chronic kidney disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for rivaroxaban reversal", "completion": "(ciraparantag | PER977) & (rivaroxaban | Xarelto) & reversal"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVision transformers for small bowel inflammation", "completion": "(ViT | \"vision transformers\") & (\"small bowel\" | SB) & inflammation & (capsule | VCE)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in traumatic brain injury surgery", "completion": "(BIS | \"bispectral index\") & (TBI | \"traumatic brain injury\") & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and mitochondrial biogenesis in sepsis", "completion": "(corticosteroids | steroids) & (mitochondrial | biogenesis) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHierarchical RL for cuff pressure", "completion": "\"hierarchical reinforcement learning\" & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome signatures", "completion": "(FMT | \"fecal microbiota transplantation\") & (microbiome | signatures)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for sleeve gastrectomy leaks", "completion": "endoscopic & vacuum & (\"sleeve gastrectomy\" | bariatric) & leaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for cancer", "completion": "(D-dimer | \"fibrin fragment\") & adjusted & cancer"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric ARDS", "completion": "(angiotensin II | Giapreza) & pediatric & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI subphenotypes in burn patients", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotypes & burns"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP in unilateral lung injury", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & (unilateral | lung) & injury"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute decompensated heart failure", "completion": "(sacubitril/valsartan | Entresto) & (ADHF | \"acute decompensated heart failure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag dosing in pregnancy", "completion": "(avatrombopag | Doptelet) & dosing & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nZero-knowledge proofs for INR verification", "completion": "(zero-knowledge | proofs) & (INR | \"international ratio\") & verification"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung-to-head ratio z-scores in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung to head ratio\" | LHR) & (Z-scores | standardized) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer recurrence", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & recurrence"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD treatment intensification", "completion": "(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (intensification | escalation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in elderly patients", "completion": "(CYP2C19 | genotype) & testing & elderly"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics as sepsis biomarker", "completion": "(PCT | procalcitonin) & kinetics & sepsis & biomarker"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in IVF pregnancies", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (IVF | \"in vitro fertilization\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS with delirium tremens", "completion": "ketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"delirium tremens\" | DT)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed simulators for pericardiocentesis", "completion": "(3D | printed) & simulators & pericardiocentesis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for carpometacarpal OA", "completion": "(autologous | micro-fragmented) & (adipose | fat) & tissue & (carpometacarpal | CMC) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy after ERCP for cholangitis", "completion": "(same-admission | immediate) & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & cholangitis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in COVID-associated hyperinflammation", "completion": "(HScore | \"HScore\") & (COVID | SARS-CoV-2) & hyperinflammation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference-based BP measurement", "completion": "(upper arm | circumference) & based & (BP | \"blood pressure\") & measurement"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMiddle cerebral artery PI in early-onset IUGR", "completion": "(MCA | \"middle cerebral artery\") & (PI | \"pulsatility index\") & (\"early onset\" | early) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in nephrotic syndrome dyslipidemia", "completion": "(PCSK9 | inhibitor) & (\"nephrotic syndrome\" | proteinuria) & dyslipidemia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and septic shock mortality", "completion": "(vitamin D | cholecalciferol) & (\"septic shock\" | sepsis) & mortality"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs placebo for type 1 HRS", "completion": "(terlipressin | vasopressin) & placebo & (\"type 1\" | HRS) & (\"hepatorenal syndrome\" | HRS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAmplatzer PFO occluder closure rates", "completion": "(Amplatzer | occluder) & (PFO | \"patent foramen ovale\") & (closure | rates)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nT2Bacteria for rapid diagnosis in immunocompromised", "completion": "(T2Bacteria | panel) & rapid & diagnosis & immunocompromised"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac light-chain amyloidosis", "completion": "(ECV | \"extracellular volume\") & (cardiac | AL) & amyloidosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time in sepsis mortality prediction", "completion": "(lactate | clearance) & time & sepsis & mortality & prediction"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS work disability", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"work disability\" | absence)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP reduction magnitude in ICH", "completion": "(systolic | BP) & reduction & magnitude & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte exhaustion", "completion": "\"self supervised learning\" & lymphocyte & exhaustion"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine dose personalization for toxic MNG", "completion": "(radioiodine | I-131) & dose & personalization & (\"toxic MNG\" | multinodular)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVicineum for BCG-unresponsive urothelial carcinoma", "completion": "(vicineum | oportuzumab) & (BCG-unresponsive | resistant) & (\"urothelial carcinoma\" | NMIBC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after coronary artery bypass", "completion": "(ticagrelor | Brilinta) & monotherapy & (\"coronary artery bypass\" | CABG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAfirma GSC for anaplastic thyroid cancer", "completion": "(Afirma | GSC) & (\"anaplastic thyroid\" | ATC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TAP block for abdominal surgery", "completion": "(liposomal | Exparel) & bupivacaine & (TAP | \"transverse abdominis plane\") & block & abdominal & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAtorvastatin for CIN prevention in NSTEMI", "completion": "(atorvastatin | Lipitor) & prevention & (CIN | \"contrast-induced nephropathy\") & (NSTEMI | \"non ST elevation MI\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAndexanet for apixaban reversal in gastrointestinal bleeding", "completion": "(andexanet | Andexxa) & (apixaban | Eliquis) & reversal & (\"gastrointestinal bleeding\" | GIB)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for capsule endoscopy video analysis", "completion": "\"few shot learning\" & (capsule | VCE) & endoscopy & video & analysis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in brain tumor surgery", "completion": "(BIS | \"bispectral index\") & (\"brain tumor\" | glioma) & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and autophagy in sepsis", "completion": "(corticosteroids | steroids) & autophagy & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMeta-learning for cuff pressure RL", "completion": "(meta-learning | meta) & cuff & pressure & (RL | \"reinforcement learning\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT response prediction models", "completion": "(FMT | \"fecal microbiota transplantation\") & response & (prediction | models)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for esophagectomy leaks", "completion": "endoscopic & vacuum & (esophagectomy | leak)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for inflammatory conditions", "completion": "(D-dimer | \"fibrin fragment\") & adjusted & (inflammatory | conditions)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric septic shock with cardiomyopathy", "completion": "(angiotensin II | Giapreza) & pediatric & (\"septic shock\" | sepsis) & cardiomyopathy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI phenotyping in burns", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenotyping & burns"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP optimization in chest trauma", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & optimization & (\"chest trauma\" | injury)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute heart failure", "completion": "(sacubitril/valsartan | Entresto) & (\"acute heart failure\" | AHF)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag for HELLP syndrome platelet count", "completion": "(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & (\"platelet count\" | PLT)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHomomorphic encryption for INR computation", "completion": "(homomorphic | encryption) & (INR | \"international ratio\") & computation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume to body weight ratio in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume to body weight\" | ratio) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer survival", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & survival"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD postoperative monitoring", "completion": "(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (postoperative | monitoring)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in acute coronary syndrome", "completion": "(CYP2C19 | genotype) & testing & (ACS | \"acute coronary syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for COVID-19 prognosis", "completion": "(PCT | procalcitonin) & kinetics & (COVID | SARS-CoV-2) & prognosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in multiple gestation", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (\"multiple gestation\" | twins)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS in critical care", "completion": "ketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"critical care\" | ICU)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed patient-specific pericardiocentesis models", "completion": "(3D | printed) & patient-specific & pericardiocentesis & models"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for erosive CMC OA", "completion": "(autologous | protein) & solution & erosive & (CMC | carpometacarpal) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIndex cholecystectomy after endoscopic retrograde cholangiopancreatography", "completion": "(index | interval) & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in systemic JIA", "completion": "(HScore | \"HScore\") & (sJIA | \"systemic juvenile idiopathic arthritis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nForearm BP measurement in clinical guidelines", "completion": "(forearm | BP) & measurement & (guidelines | recommendations)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUmbilical artery absent end-diastolic flow in IUGR", "completion": "(umbilical | UA) & artery & (\"absent end diastolic flow\" | AEDF) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in chronic inflammatory diseases", "completion": "(PCSK9 | inhibitor) & (chronic | inflammatory) & diseases"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ICU-acquired weakness", "completion": "(vitamin D | cholecalciferol) & (\"ICU acquired weakness\" | myopathy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs albumin for HRS", "completion": "(terlipressin | vasopressin) & albumin & (HRS | \"hepatorenal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGore Cardioform ASD occluder", "completion": "(Gore | Cardioform) & device & (ASD | \"atrial septal defect\") & occluder"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBioFire for sepsis in burns", "completion": "(BioFire | FilmArray) & sepsis & burns"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in radiation-induced cardiotoxicity", "completion": "(ECV | \"extracellular volume\") & (radiation | induced) & cardiotoxicity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio in septic shock resuscitation", "completion": "(lactate/albumin | ratio) & (\"septic shock\" | sepsis) & resuscitation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS productivity loss", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"productivity loss\" | economic)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and hematoma volume in ICH", "completion": "(systolic | BP) & (\"hematoma volume\" | size) & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte heterogeneity", "completion": "\"self supervised learning\" & lymphocyte & heterogeneity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCalculated radioiodine dose for Graves' disease", "completion": "(calculated | MIRD) & (radioiodine | I-131) & dose & (\"Graves'' disease\" | hyperthyroidism)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAdstiladrin for BCG-unresponsive NMIBC", "completion": "(Adstiladrin | nadofaragene) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after DES", "completion": "(ticagrelor | Brilinta) & monotherapy & (DES | \"drug-eluting stent\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRosettaGX Reveal for papillary carcinoma", "completion": "(RosettaGX | Reveal) & (\"papillary carcinoma\" | PTC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in breast reconstruction pain", "completion": "(liposomal | Exparel) & bupivacaine & (\"breast reconstruction\" | mastectomy) & pain"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHigh-dose statin for CIN prevention meta-analysis", "completion": "(high dose | statin) & prevention & (CIN | \"contrast-induced nephropathy\") & meta-analysis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIdarucizumab for dabigatran reversal in GI bleeding", "completion": "(idarucizumab | Praxbind) & (dabigatran | Pradaxa) & reversal & (\"GI bleeding\" | hemorrhage)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMeta-learning for few-shot capsule endoscopy", "completion": "(meta-learning | few-shot) & (capsule | VCE) & endoscopy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in deep brain stimulation surgery", "completion": "(BIS | \"bispectral index\") & (DBS | \"deep brain stimulation\") & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and metabolic reprogramming in sepsis", "completion": "(corticosteroids | steroids) & (metabolic | reprogramming) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDistributed RL for cuff pressure", "completion": "(distributed | reinforcement) & learning & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome engraftment kinetics", "completion": "(FMT | \"fecal microbiota transplantation\") & (microbiome | engraftment) & kinetics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-bariatric leaks", "completion": "endoscopic & vacuum & (\"post bariatric\" | surgery) & leaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWells score in cancer patients for PE", "completion": "(Wells | score) & cancer & (PE | \"pulmonary embolism\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in congenital heart disease with shock", "completion": "(angiotensin II | Giapreza) & (\"congenital heart disease\" | CHD) & shock"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI subphenotypes in cardiac surgery", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotypes & (\"cardiac surgery\" | CABG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in pediatric pneumonia", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & pediatric & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute myocarditis recovery", "completion": "(sacubitril/valsartan | Entresto) & acute & myocarditis & recovery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag for HELLP thrombocytopenia", "completion": "(lusutrombopag | Mulpleta) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & thrombocytopenia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSecure enclaves for INR processing", "completion": "(secure | enclaves) & (INR | \"international ratio\") & processing"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume percent predicted in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume\" | percent) & predicted & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer local recurrence", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & (\"local recurrence\" | recurrence)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC treatment response", "completion": "(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (treatment | response)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in primary care", "completion": "(CYP2C19 | genotype) & testing & (\"primary care\" | community)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for bacterial coinfection in COVID", "completion": "(PCT | procalcitonin) & kinetics & (bacterial | coinfection) & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in ART pregnancies", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (ART | \"assisted reproductive technology\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS in intensive care", "completion": "ketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"intensive care\" | ICU)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed simulators for pericardiocentesis education", "completion": "(3D | printed) & simulators & pericardiocentesis & education"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for thumb base OA", "completion": "(autologous | protein) & solution & (\"thumb base\" | CMC) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy after ERCP for gallstones", "completion": "(same-admission | immediate) & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & gallstones"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in COVID macrophage activation", "completion": "(HScore | \"HScore\") & (COVID | SARS-CoV-2) & (\"macrophage activation\" | MAS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThigh BP measurement in obesity", "completion": "(thigh | BP) & measurement & obesity"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCerebral-placental ratio in early-onset IUGR", "completion": "(cerebral-placental | CPR) & ratio & (\"early onset\" | early) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in HIV on ART", "completion": "(PCSK9 | inhibitor) & (HIV | \"human immunodeficiency virus\") & (ART | \"antiretroviral therapy\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and delirium in ICU", "completion": "(vitamin D | cholecalciferol) & delirium & (ICU | \"intensive care\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs terlipressin/albumin for HRS", "completion": "(terlipressin | vasopressin) & (terlipressin/albumin | combination) & (HRS | \"hepatorenal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOcclutech PFO occluder long-term", "completion": "(Occlutech | occluder) & (PFO | \"patent foramen ovale\") & \"long term\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAccelerate Pheno for Gram-negative bacteremia", "completion": "(Accelerate | Pheno) & (\"Gram-negative\" | bacteremia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in chemotherapy-related cardiac dysfunction", "completion": "(ECV | \"extracellular volume\") & (chemotherapy | induced) & (\"cardiac dysfunction\" | cardiotoxicity)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time vs ScvO2", "completion": "(lactate | clearance) & time & (ScvO2 | \"central venous oxygen saturation\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS return-to-sport", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"return to sport\" | athletics)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and edema in ICH", "completion": "(systolic | BP) & edema & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte trajectories", "completion": "\"self supervised learning\" & lymphocyte & trajectories"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMIRD for Graves' disease radioiodine", "completion": "(MIRD | \"medical internal radiation dose\") & (\"Graves'' disease\" | hyperthyroidism) & (radioiodine | I-131)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nInstiladrin for CIS NMIBC", "completion": "(Instiladrin | nadofaragene) & (CIS | \"carcinoma in situ\") & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after complex PCI", "completion": "(ticagrelor | Brilinta) & monotherapy & (complex | high-risk) & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAfirma Xpression for thyroid nodules", "completion": "(Afirma | Xpression) & (\"thyroid nodules\" | nodules)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TAP block efficacy", "completion": "(liposomal | Exparel) & bupivacaine & (TAP | \"transverse abdominis plane\") & block & efficacy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAtorvastatin for CIN prevention meta-analysis", "completion": "(atorvastatin | Lipitor) & prevention & (CIN | \"contrast-induced nephropathy\") & meta-analysis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAndexanet for factor Xa inhibitor reversal in GIB", "completion": "(andexanet | Andexxa) & (\"factor Xa\" | inhibitor) & reversal & (GIB | \"gastrointestinal bleeding\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for capsule endoscopy video classification", "completion": "\"few shot learning\" & (capsule | VCE) & endoscopy & video & classification"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in epilepsy monitoring", "completion": "(BIS | \"bispectral index\") & (epilepsy | seizures) & monitoring"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and mitochondrial dynamics in sepsis", "completion": "(corticosteroids | steroids) & (mitochondrial | dynamics) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHierarchical meta-RL for cuff pressure", "completion": "\"hierarchical meta reinforcement learning\" & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome predictors of cure", "completion": "(FMT | \"fecal microbiota transplantation\") & (microbiome | predictors) & cure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-esophagectomy leaks", "completion": "endoscopic & vacuum & (\"post esophagectomy\" | esophagectomy) & leaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for DVT history", "completion": "(D-dimer | \"fibrin fragment\") & adjusted & (DVT | \"deep vein thrombosis\") & history"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric cardiogenic shock", "completion": "(angiotensin II | Giapreza) & pediatric & (cardiogenic | shock)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI subphenotypes in sepsis", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotypes & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in unilateral ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (unilateral | ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute decompensated HF", "completion": "(sacubitril/valsartan | Entresto) & (ADHF | \"acute decompensated heart failure\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag in HELLP with severe thrombocytopenia", "completion": "(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & (severe | thrombocytopenia)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nZero-knowledge proofs for INR data sharing", "completion": "(zero-knowledge | proofs) & (INR | \"international ratio\") & (\"data sharing\" | privacy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung area to head circumference ratio in CDH", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung area to head circumference\" | LHR) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer distant recurrence", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & (\"distant recurrence\" | metastasis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD treatment de-escalation", "completion": "(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (\"de escalation\" | reduction)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in anticoagulated patients", "completion": "(CYP2C19 | genotype) & testing & anticoagulated"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for antibiotic stewardship in VAP", "completion": "(PCT | procalcitonin) & kinetics & (antibiotic | stewardship) & (VAP | \"ventilator associated pneumonia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in preterm PROM", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (PROM | \"premature rupture of membranes\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS in mechanically ventilated patients", "completion": "ketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"mechanically ventilated\" | MV)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed patient-specific pericardiocentesis guides", "completion": "(3D | printed) & patient-specific & pericardiocentesis & guides"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for thumb CMC OA", "completion": "(autologous | micro-fragmented) & (adipose | fat) & tissue & (thumb | CMC) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEarly cholecystectomy after ERCP for acute cholangitis", "completion": "early & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & (\"acute cholangitis\" | cholangitis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in HLH secondary to malignancy", "completion": "(HScore | \"HScore\") & (HLH | \"hemophagocytic lymphohistiocytosis\") & (secondary | malignancy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference-based cuff size guidelines", "completion": "(upper arm | circumference) & based & cuff & size & guidelines"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMiddle cerebral artery Doppler in IUGR management", "completion": "(MCA | \"middle cerebral artery\") & Doppler & (IUGR | \"intrauterine growth restriction\") & management"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in renal transplant recipients", "completion": "(PCSK9 | inhibitor) & (\"renal transplant\" | transplantation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ARDS in COVID", "completion": "(vitamin D | cholecalciferol) & (ARDS | \"acute respiratory distress syndrome\") & (COVID | SARS-CoV-2)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for type 2 HRS", "completion": "(terlipressin | vasopressin) & (\"type 2\" | HRS) & (\"hepatorenal syndrome\" | HRS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAmplatzer PFO occluder residual shunt", "completion": "(Amplatzer | occluder) & (PFO | \"patent foramen ovale\") & (\"residual shunt\" | shunt)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nT2Candida for candidemia in ICU", "completion": "(T2Candida | panel) & candidemia & (ICU | \"intensive care\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac amyloidosis monitoring", "completion": "(ECV | \"extracellular volume\") & (\"cardiac amyloidosis\" | AL) & monitoring"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time for sepsis mortality", "completion": "(lactate | clearance) & time & sepsis & mortality"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS surgical outcomes", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (surgical | outcomes)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and midline shift in ICH", "completion": "(systolic | BP) & (\"midline shift\" | MLS) & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte maturation", "completion": "\"self supervised learning\" & lymphocyte & maturation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRadioiodine dose calculation for toxic nodular goiter", "completion": "(radioiodine | I-131) & dose & calculation & (\"toxic nodular goiter\" | MNG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVicinium for high-grade Ta NMIBC", "completion": "(vicinium | oportuzumab) & (\"high grade\" | Ta) & (NMIBC | \"non-muscle invasive bladder cancer\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after CABG", "completion": "(ticagrelor | Brilinta) & monotherapy & (CABG | \"coronary artery bypass\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAfirma GSC for follicular thyroid lesions", "completion": "(Afirma | GSC) & (follicular | lesions) & thyroid"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap donor site pain", "completion": "(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & (\"donor site\" | abdomen) & pain"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin for CIN prevention in PCI", "completion": "(rosuvastatin | Crestor) & prevention & (CIN | \"contrast-induced nephropathy\") & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for apixaban reversal in ICH", "completion": "(ciraparantag | PER977) & (apixaban | Eliquis) & reversal & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVision transformers for capsule endoscopy frame classification", "completion": "(ViT | \"vision transformers\") & (capsule | VCE) & endoscopy & frame & classification"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in brain aneurysm surgery", "completion": "(BIS | \"bispectral index\") & (\"brain aneurysm\" | aneurysm) & surgery"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and immunometabolism in sepsis", "completion": "(corticosteroids | steroids) & (immunometabolism | metabolism) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFederated RL for cuff pressure optimization", "completion": "\"federated reinforcement learning\" & cuff & pressure & optimization"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome functional profiles", "completion": "(FMT | \"fecal microbiota transplantation\") & (microbiome | functional) & profiles"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-pancreatectomy leaks", "completion": "endoscopic & vacuum & (\"post pancreatectomy\" | pancreatectomy) & leaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for autoimmune disease", "completion": "(D-dimer | \"fibrin fragment\") & adjusted & (autoimmune | disease)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric pulmonary hypertension", "completion": "(angiotensin II | Giapreza) & pediatric & (\"pulmonary hypertension\" | PAH)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI endotypes in cardiac surgery", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & endotypes & (\"cardiac surgery\" | CABG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in COVID ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute HF with reduced EF", "completion": "(sacubitril/valsartan | Entresto) & (\"acute HF\" | AHF) & (HFrEF | \"heart failure with reduced ejection fraction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag dosing in pregnancy", "completion": "(lusutrombopag | Mulpleta) & dosing & pregnancy"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHomomorphic encryption for INR analytics", "completion": "(homomorphic | encryption) & (INR | \"international ratio\") & analytics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume to fetal body volume ratio", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume to fetal body volume\" | ratio)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer disease-free survival", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & (\"disease free survival\" | DFS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC deep remission", "completion": "(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (\"deep remission\" | remission)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in stroke patients", "completion": "(CYP2C19 | genotype) & testing & stroke"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for antibiotic duration in sepsis", "completion": "(PCT | procalcitonin) & kinetics & antibiotics & duration & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in women with prior PTB", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (prior | history)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine vs propofol for AWS", "completion": "ketamine & (propofol | Diprivan) & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed phantoms for pericardiocentesis simulation", "completion": "(3D | printed) & phantoms & pericardiocentesis & simulation"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for erosive thumb OA", "completion": "(autologous | protein) & solution & erosive & (thumb | CMC) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy after endoscopic stone extraction", "completion": "(same-admission | immediate) & cholecystectomy & endoscopic & (\"stone extraction\" | removal)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in EBV-associated HLH", "completion": "(HScore | \"HScore\") & (EBV | \"Epstein Barr virus\") & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThigh BP in clinical practice", "completion": "(thigh | BP) & measurement & (\"clinical practice\" | accuracy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDuctus venosus Doppler in early-onset IUGR", "completion": "(ductus venosus | DV) & Doppler & (\"early onset\" | early) & (IUGR | \"intrauterine growth restriction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in solid organ transplant", "completion": "(PCSK9 | inhibitor) & (\"solid organ transplant\" | transplantation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ventilator-associated pneumonia", "completion": "(vitamin D | cholecalciferol) & (VAP | \"ventilator associated pneumonia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for acute kidney injury in cirrhosis", "completion": "(terlipressin | vasopressin) & (AKI | \"acute kidney injury\") & cirrhosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nGore Cardioform ASD device", "completion": "(Gore | Cardioform) & device & (ASD | \"atrial septal defect\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBioFire BCID2 for pediatric ICU sepsis", "completion": "(BioFire | BCID2) & pediatric & (ICU | \"intensive care\") & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in cardiotoxicity monitoring", "completion": "(ECV | \"extracellular volume\") & (cardiotoxicity | monitoring)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio for septic shock mortality", "completion": "(lactate/albumin | ratio) & (\"septic shock\" | sepsis) & mortality"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS economic evaluation", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (economic | evaluation)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and perihematomal edema", "completion": "(systolic | BP) & (\"perihematomal edema\" | edema) & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte spatial organization", "completion": "\"self supervised learning\" & lymphocyte & (spatial | organization)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nActivity-based dosing for Graves' radioiodine", "completion": "(activity | MIRD) & based & dosing & (\"Graves'' disease\" | hyperthyroidism) & (radioiodine | I-131)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nInstiladrin for CIS bladder cancer", "completion": "(Instiladrin | nadofaragene) & (CIS | \"carcinoma in situ\") & (\"bladder cancer\" | NMIBC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after left main stenting", "completion": "(ticagrelor | Brilinta) & monotherapy & (\"left main\" | LM) & stenting"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRosettaGX Reveal for indeterminate FNA", "completion": "(RosettaGX | Reveal) & (indeterminate | FNA | \"fine needle aspiration\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap analgesia", "completion": "(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & analgesia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHigh-dose atorvastatin for CIN in CKD", "completion": "(high dose | statin) & atorvastatin & (CIN | \"contrast-induced nephropathy\") & (CKD | \"chronic kidney disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIdarucizumab for ICH on dabigatran", "completion": "(idarucizumab | Praxbind) & (ICH | \"intracerebral hemorrhage\") & (dabigatran | Pradaxa)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for capsule endoscopy lesion detection", "completion": "\"few shot learning\" & (capsule | VCE) & endoscopy & lesion & detection"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in awake craniotomy", "completion": "(BIS | \"bispectral index\") & (\"awake craniotomy\" | craniotomy)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and epigenetic changes in sepsis", "completion": "(corticosteroids | steroids) & (epigenetic | changes) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDistributed meta-RL for cuff pressure", "completion": "\"distributed meta reinforcement learning\" & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome metabolic pathways", "completion": "(FMT | \"fecal microbiota transplantation\") & (microbiome | metabolic) & pathways"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-gastrectomy leaks", "completion": "endoscopic & vacuum & (\"post gastrectomy\" | gastrectomy) & leaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for liver disease", "completion": "(D-dimer | \"fibrin fragment\") & adjusted & (\"liver disease\" | cirrhosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric septic shock with MODS", "completion": "(angiotensin II | Giapreza) & pediatric & (\"septic shock\" | sepsis) & (MODS | \"multiple organ dysfunction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI subphenotypes in burn sepsis", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotypes & (burn | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in trauma ARDS", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (trauma | injury) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute HF with preserved EF", "completion": "(sacubitril/valsartan | Entresto) & (\"acute HF\" | AHF) & (HFpEF | \"heart failure with preserved ejection fraction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag vs standard care in HELLP", "completion": "(avatrombopag | Doptelet) & (\"standard care\" | conventional) & (HELLP | \"hemolysis elevated liver enzymes low platelets\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSecure multi-party computation for INR analytics", "completion": "(secure | multi-party) & computation & (INR | \"international ratio\") & analytics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume Z-scores in congenital diaphragmatic hernia", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume\" | Z-scores) & (CDH | \"congenital diaphragmatic hernia\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer overall survival", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & (\"overall survival\" | OS)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD mucosal healing", "completion": "(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (\"mucosal healing\" | healing)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in elderly with ACS", "completion": "(CYP2C19 | genotype) & testing & elderly & (ACS | \"acute coronary syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for bacterial sepsis", "completion": "(PCT | procalcitonin) & kinetics & (bacterial | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in women with short cervix", "completion": "(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (\"short cervix\" | cervical)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nKetamine-propofol combination for severe AWS", "completion": "(ketamine | propofol) & combination & severe & (AWS | \"alcohol withdrawal syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n3D printed simulators for pericardiocentesis training", "completion": "(3D | printed) & simulators & pericardiocentesis & training"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for erosive thumb OA", "completion": "(autologous | micro-fragmented) & (adipose | fat) & tissue & erosive & (thumb | CMC) & osteoarthritis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEarly cholecystectomy after endoscopic sphincterotomy for choledocholithiasis", "completion": "early & cholecystectomy & (\"endoscopic sphincterotomy\" | ERCP) & choledocholithiasis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHScore in malignancy-associated HLH", "completion": "(HScore | \"HScore\") & (malignancy | associated) & (HLH | \"hemophagocytic lymphohistiocytosis\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThigh BP measurement guidelines", "completion": "(thigh | BP) & measurement & guidelines"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCerebroplacental ratio in IUGR prognosis", "completion": "(cerebroplacental | CPR) & ratio & (IUGR | \"intrauterine growth restriction\") & prognosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in inflammatory bowel disease", "completion": "(PCSK9 | inhibitor) & (IBD | \"inflammatory bowel disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVitamin D and COVID-19 ARDS", "completion": "(vitamin D | cholecalciferol) & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for acute variceal hemorrhage", "completion": "(terlipressin | vasopressin) & acute & (\"variceal hemorrhage\" | bleeding)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nOcclutech Figulla Flex II PFO", "completion": "(Occlutech | Figulla) & device & (PFO | \"patent foramen ovale\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSeptiFast for sepsis in neutropenia", "completion": "(SeptiFast | PCR) & sepsis & neutropenia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nECV in monitoring cardiac sarcoid", "completion": "(ECV | \"extracellular volume\") & monitoring & (\"cardiac sarcoid\" | sarcoidosis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time prognostic value in septic shock", "completion": "(lactate | clearance) & time & (prognostic | value) & (\"septic shock\" | sepsis)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS prevention cost-effectiveness", "completion": "\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & prevention & (cost-effectiveness | economic)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSystolic BP reduction and ICH expansion", "completion": "(systolic | BP) & reduction & (ICH | \"intracerebral hemorrhage\") & expansion"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte transcriptomics", "completion": "\"self supervised learning\" & lymphocyte & transcriptomics"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nMIRD vs standard dosing for toxic adenoma", "completion": "(MIRD | \"medical internal radiation dose\") & (standard | dosing) & (\"toxic adenoma\" | nodule)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCG0070 for BCG-unresponsive bladder cancer", "completion": "(CG0070 | oncolytic) & (BCG-unresponsive | resistant) & (\"bladder cancer\" | NMIBC)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after ACS with PCI", "completion": "(ticagrelor | Brilinta) & monotherapy & (ACS | \"acute coronary syndrome\") & (PCI | \"percutaneous coronary intervention\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nThyraMIR for thyroid cancer diagnosis", "completion": "(ThyraMIR | miRNA) & (\"thyroid cancer\" | malignancy) & diagnosis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TRAM flap donor site analgesia", "completion": "(liposomal | Exparel) & bupivacaine & (TRAM | \"breast reconstruction\") & (\"donor site\" | abdomen) & analgesia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin for CIN prevention in renal impairment", "completion": "(rosuvastatin | Crestor) & prevention & (CIN | \"contrast-induced nephropathy\") & (\"renal impairment\" | CKD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for edoxaban reversal in ICH", "completion": "(ciraparantag | PER977) & (edoxaban | Savaysa) & reversal & (ICH | \"intracerebral hemorrhage\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nVision transformers for capsule endoscopy video summarization", "completion": "(ViT | \"vision transformers\") & (capsule | VCE) & endoscopy & video & summarization"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nBIS in pediatric brain surgery", "completion": "(BIS | \"bispectral index\") & pediatric & (\"brain surgery\" | neurosurgery)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and gut microbiome in sepsis", "completion": "(corticosteroids | steroids) & (\"gut microbiome\" | microbiota) & sepsis"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFederated meta-learning for cuff pressure", "completion": "\"federated meta learning\" & cuff & pressure"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome virome", "completion": "(FMT | \"fecal microbiota transplantation\") & (microbiome | virome)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-Whipple leaks", "completion": "endoscopic & vacuum & (\"post Whipple\" | pancreaticoduodenectomy) & leaks"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for inflammatory bowel disease", "completion": "(D-dimer | \"fibrin fragment\") & adjusted & (IBD | \"inflammatory bowel disease\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric congenital heart surgery", "completion": "(angiotensin II | Giapreza) & pediatric & (\"congenital heart surgery\" | CHD)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]\u2022[IGFBP7] for AKI phenotyping in cardiac surgery", "completion": "([TIMP-2]\u2022[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenotyping & (\"cardiac surgery\" | CABG)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in COVID pneumonia", "completion": "(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (COVID | SARS-CoV-2) & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute HF with mid-range EF", "completion": "(sacubitril/valsartan | Entresto) & (\"acute HF\" | AHF) & (HFmrEF | \"heart failure with mid-range ejection fraction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag for HELLP syndrome outcomes", "completion": "(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & outcomes"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDifferential privacy for INR datasets", "completion": "(differential | privacy) & (INR | \"international ratio\") & datasets"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFetal MRI observed/expected total fetal lung volume", "completion": "(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"observed/expected total fetal lung volume\" | O/E TFLV)"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer quality-adjusted life years", "completion": "robotic & (\"rectal cancer\" | rectum) & surgery & (QALY | \"quality adjusted life years\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC endoscopic improvement", "completion": "(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (endoscopic | improvement)"}
